/**
 * Voice Command Processing System
 * Handles voice recognition, command parsing, and action execution
 */

import { getAIService } from "./ai-providers";
import { prisma } from "./database";
import crypto from "crypto";

export interface VoiceCommand {
  id: string;
  text: string;
  confidence: number;
  action: string;
  parameters?: Record<string, any>;
  timestamp: Date;
  userId?: string;
}

export interface VoiceCommandResult {
  success: boolean;
  command: VoiceCommand;
  response?: string;
  error?: string;
}

export class VoiceCommandProcessor {
  private static instance: VoiceCommandProcessor;
  private aiService = getAIService();
  private commandHistory: Map<string, VoiceCommand[]> = new Map();
  private activeListeners: Map<string, (result: VoiceCommandResult) => void> =
    new Map();

  static getInstance(): VoiceCommandProcessor {
    if (!VoiceCommandProcessor.instance) {
      VoiceCommandProcessor.instance = new VoiceCommandProcessor();
    }
    return VoiceCommandProcessor.instance;
  }

  /**
   * Process audio input and extract voice command
   */
  async processAudioCommand(
    audioBlob: Blob,
    userId?: string,
    context?: Record<string, any>,
  ): Promise<VoiceCommandResult> {
    try {
      // Transcribe audio to text
      const transcription = await this.transcribeAudio(audioBlob, userId);

      if (!transcription.trim()) {
        return {
          success: false,
          command: this.createEmptyCommand(userId),
          error: "No speech detected",
        };
      }

      // Parse command from transcription
      const command = await this.parseCommand(transcription, userId, context);

      // Execute command
      const result = await this.executeCommand(command, context);

      // Store in history
      this.addToHistory(userId || "anonymous", command);

      // Track analytics
      await this.trackCommandUsage(command, result.success, userId);

      return result;
    } catch (error) {
      console.error("Voice command processing failed:", error);
      return {
        success: false,
        command: this.createEmptyCommand(userId),
        error: error instanceof Error ? error.message : "Processing failed",
      };
    }
  }

  /**
   * Process text command directly
   */
  async processTextCommand(
    text: string,
    userId?: string,
    context?: Record<string, any>,
  ): Promise<VoiceCommandResult> {
    try {
      const command = await this.parseCommand(text, userId, context);
      const result = await this.executeCommand(command, context);

      this.addToHistory(userId || "anonymous", command);
      await this.trackCommandUsage(command, result.success, userId);

      return result;
    } catch (error) {
      console.error("Text command processing failed:", error);
      return {
        success: false,
        command: this.createEmptyCommand(userId, text),
        error: error instanceof Error ? error.message : "Processing failed",
      };
    }
  }

  /**
   * Transcribe audio using AI service
   */
  private async transcribeAudio(
    audioBlob: Blob,
    userId?: string,
  ): Promise<string> {
    try {
      const result = await this.aiService.processVoiceCommand(
        audioBlob,
        userId,
      );
      return result.command;
    } catch (error) {
      console.error("Audio transcription failed:", error);
      throw new Error("Failed to transcribe audio");
    }
  }

  /**
   * Parse command text into structured command
   */
  private async parseCommand(
    text: string,
    userId?: string,
    context?: Record<string, any>,
  ): Promise<VoiceCommand> {
    const normalizedText = text.toLowerCase().trim();

    // Define command patterns with enhanced matching
    const commandPatterns = [
      {
        pattern: /^(explain|tell me about|what is|what does) (.+)$/,
        action: "explain_element",
        confidence: 0.9,
        extractor: (match: RegExpMatchArray) => ({ target: match[2] }),
      },
      {
        pattern: /^(help me with|show me how to) (.+)$/,
        action: "explain_element",
        confidence: 0.8,
        extractor: (match: RegExpMatchArray) => ({ target: match[2] }),
      },
      {
        pattern: /^(start|begin|launch) (tutorial|guide|walkthrough)$/,
        action: "start_tutorial",
        confidence: 0.95,
        extractor: () => ({}),
      },
      {
        pattern: /^(next|continue|forward)( step)?$/,
        action: "next_step",
        confidence: 0.9,
        extractor: () => ({}),
      },
      {
        pattern: /^(previous|back|go back)( step)?$/,
        action: "previous_step",
        confidence: 0.9,
        extractor: () => ({}),
      },
      {
        pattern: /^(stop|end|quit|exit) (tutorial|guide)$/,
        action: "stop_tutorial",
        confidence: 0.95,
        extractor: () => ({}),
      },
      {
        pattern: /^(pause|hold) (tutorial|guide)$/,
        action: "pause_tutorial",
        confidence: 0.9,
        extractor: () => ({}),
      },
      {
        pattern: /^(resume|continue) (tutorial|guide)$/,
        action: "resume_tutorial",
        confidence: 0.9,
        extractor: () => ({}),
      },
      {
        pattern: /^(repeat|say again|replay)$/,
        action: "repeat_step",
        confidence: 0.85,
        extractor: () => ({}),
      },
      {
        pattern: /^(click|press|tap) (.+)$/,
        action: "click_element",
        confidence: 0.8,
        extractor: (match: RegExpMatchArray) => ({ target: match[2] }),
      },
      {
        pattern: /^(scroll|move) (up|down|left|right)$/,
        action: "scroll",
        confidence: 0.8,
        extractor: (match: RegExpMatchArray) => ({ direction: match[2] }),
      },
    ];

    // Find best matching pattern
    let bestMatch: {
      command: VoiceCommand;
      confidence: number;
    } | null = null;

    for (const pattern of commandPatterns) {
      const match = normalizedText.match(pattern.pattern);
      if (match) {
        const parameters = pattern.extractor(match);
        const command: VoiceCommand = {
          id: `cmd_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
          text: text,
          confidence: pattern.confidence,
          action: pattern.action,
          parameters,
          timestamp: new Date(),
          userId,
        };

        if (!bestMatch || pattern.confidence > bestMatch.confidence) {
          bestMatch = { command, confidence: pattern.confidence };
        }
      }
    }

    // If no pattern matches or confidence is low, use AI parsing
    if (!bestMatch || bestMatch.confidence < 0.7) {
      try {
        const aiParsed = await this.parseWithAI(text, userId, context);
        if (!bestMatch || aiParsed.confidence > bestMatch.confidence) {
          return aiParsed;
        }
      } catch (error) {
        console.warn("AI command parsing failed:", error);
      }
    }

    return bestMatch?.command || this.createUnknownCommand(text, userId);
  }

  /**
   * Use AI to parse complex commands
   */
  private async parseWithAI(
    text: string,
    userId?: string,
    context?: Record<string, any>,
  ): Promise<VoiceCommand> {
    const prompt = `Parse this voice command and determine the user's intent:

Command: "${text}"
Context: ${JSON.stringify(context || {})}

Available actions:
- explain_element: User wants explanation of a UI element
- start_tutorial: User wants to start a tutorial
- next_step: User wants to go to next tutorial step
- previous_step: User wants to go back
- stop_tutorial: User wants to stop the tutorial
- pause_tutorial: User wants to pause
- resume_tutorial: User wants to resume
- repeat_step: User wants to repeat current step
- click_element: User wants to click on something
- scroll: User wants to scroll in a direction
- unknown: Command not recognized

Respond with JSON only:
{
  "action": "action_name",
  "confidence": 0.0-1.0,
  "parameters": {}
}`;

    try {
      // Use AI service to parse command
      let response = "";
      const messageId = `parse_${Date.now()}`;

      const eventListener = (event: any) => {
        if (event.type === "TextMessageContent") {
          response += event.delta;
        }
      };

      this.aiService.onEvent(eventListener);

      await this.aiService.streamCompletion(prompt, messageId, {
        userId,
        maxTokens: 200,
        temperature: 0.1,
      });

      // Parse AI response
      const cleanResponse = response.trim().replace(/```json\n?|```\n?/g, "");
      const parsed = JSON.parse(cleanResponse);

      return {
        id: `cmd_${Date.now()}_${crypto.randomUUID().slice(0, 8)}`,
        text: text,
        confidence: Math.max(0.1, Math.min(1.0, parsed.confidence || 0.5)),
        action: parsed.action || "unknown",
        parameters: parsed.parameters || {},
        timestamp: new Date(),
        userId,
      };
    } catch (error) {
      console.error("AI command parsing error:", error);
      return this.createUnknownCommand(text, userId);
    }
  }

  /**
   * Execute parsed command
   */
  private async executeCommand(
    command: VoiceCommand,
    context?: Record<string, any>,
  ): Promise<VoiceCommandResult> {
    try {
      let response = "";

      switch (command.action) {
        case "explain_element":
          response = await this.handleExplainElement(command, context);
          break;

        case "start_tutorial":
          response = await this.handleStartTutorial(command, context);
          break;

        case "next_step":
          response = await this.handleNextStep(command, context);
          break;

        case "previous_step":
          response = await this.handlePreviousStep(command, context);
          break;

        case "stop_tutorial":
          response = await this.handleStopTutorial(command, context);
          break;

        case "pause_tutorial":
          response = await this.handlePauseTutorial(command, context);
          break;

        case "resume_tutorial":
          response = await this.handleResumeTutorial(command, context);
          break;

        case "repeat_step":
          response = await this.handleRepeatStep(command, context);
          break;

        case "click_element":
          response = await this.handleClickElement(command, context);
          break;

        case "scroll":
          response = await this.handleScroll(command, context);
          break;

        default:
          response = "I didn't understand that command. Please try again.";
          break;
      }

      return {
        success: true,
        command,
        response,
      };
    } catch (error) {
      console.error("Command execution failed:", error);
      return {
        success: false,
        command,
        error: error instanceof Error ? error.message : "Execution failed",
      };
    }
  }

  // Command handlers
  private async handleExplainElement(
    command: VoiceCommand,
    context?: Record<string, any>,
  ): Promise<string> {
    const target = command.parameters?.target || "this element";
    // Trigger AI explanation
    if (command.userId) {
      await this.aiService.explainElement(
        target,
        `Explain ${target}`,
        context?.pageUrl || window.location.href,
        `run_${Date.now()}`,
        `thread_${Date.now()}`,
        command.userId,
      );
    }
    return `I'll explain ${target} for you.`;
  }

  private async handleStartTutorial(
    command: VoiceCommand,
    context?: Record<string, any>,
  ): Promise<string> {
    // Implementation for starting tutorial
    return "Starting tutorial now.";
  }

  private async handleNextStep(
    command: VoiceCommand,
    context?: Record<string, any>,
  ): Promise<string> {
    // Implementation for next step
    return "Moving to the next step.";
  }

  private async handlePreviousStep(
    command: VoiceCommand,
    context?: Record<string, any>,
  ): Promise<string> {
    // Implementation for previous step
    return "Going back to the previous step.";
  }

  private async handleStopTutorial(
    command: VoiceCommand,
    context?: Record<string, any>,
  ): Promise<string> {
    // Implementation for stopping tutorial
    return "Tutorial stopped.";
  }

  private async handlePauseTutorial(
    command: VoiceCommand,
    context?: Record<string, any>,
  ): Promise<string> {
    // Implementation for pausing tutorial
    return "Tutorial paused.";
  }

  private async handleResumeTutorial(
    command: VoiceCommand,
    context?: Record<string, any>,
  ): Promise<string> {
    // Implementation for resuming tutorial
    return "Tutorial resumed.";
  }

  private async handleRepeatStep(
    command: VoiceCommand,
    context?: Record<string, any>,
  ): Promise<string> {
    // Implementation for repeating step
    return "Repeating the current step.";
  }

  private async handleClickElement(
    command: VoiceCommand,
    context?: Record<string, any>,
  ): Promise<string> {
    const target = command.parameters?.target || "element";
    // Implementation for clicking element
    return `Clicking on ${target}.`;
  }

  private async handleScroll(
    command: VoiceCommand,
    context?: Record<string, any>,
  ): Promise<string> {
    const direction = command.parameters?.direction || "down";
    // Implementation for scrolling
    return `Scrolling ${direction}.`;
  }

  // Utility methods
  private createEmptyCommand(userId?: string, text: string = ""): VoiceCommand {
    return {
      id: `cmd_${Date.now()}_empty`,
      text,
      confidence: 0,
      action: "unknown",
      timestamp: new Date(),
      userId,
    };
  }

  private createUnknownCommand(text: string, userId?: string): VoiceCommand {
    return {
      id: `cmd_${Date.now()}_unknown`,
      text,
      confidence: 0.1,
      action: "unknown",
      timestamp: new Date(),
      userId,
    };
  }

  private addToHistory(userId: string, command: VoiceCommand): void {
    if (!this.commandHistory.has(userId)) {
      this.commandHistory.set(userId, []);
    }

    const history = this.commandHistory.get(userId)!;
    history.push(command);

    // Keep only last 50 commands
    if (history.length > 50) {
      history.shift();
    }
  }

  private async trackCommandUsage(
    command: VoiceCommand,
    success: boolean,
    userId?: string,
  ): Promise<void> {
    if (!userId) return;

    try {
      await prisma.analytics.create({
        data: {
          userId,
          action: "voice_command_processed",
          metadata: {
            commandId: command.id,
            action: command.action,
            confidence: command.confidence,
            success,
            text: command.text,
            parameters: command.parameters,
            timestamp: command.timestamp.toISOString(),
          },
        },
      });
    } catch (error) {
      console.error("Failed to track command usage:", error);
    }
  }

  // Public methods
  getCommandHistory(userId: string): VoiceCommand[] {
    return this.commandHistory.get(userId) || [];
  }

  clearHistory(userId: string): void {
    this.commandHistory.delete(userId);
  }

  addCommandListener(
    id: string,
    listener: (result: VoiceCommandResult) => void,
  ): void {
    this.activeListeners.set(id, listener);
  }

  removeCommandListener(id: string): void {
    this.activeListeners.delete(id);
  }
}

// Export singleton instance
export const voiceCommandProcessor = VoiceCommandProcessor.getInstance();
