"use client";

import React, { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { useToast } from "@/components/ui/use-toast";
import {
  Award,
  Download,
  Share,
  Eye,
  Calendar,
  Clock,
  Star,
  Shield,
  Copy,
  ExternalLink,
} from "lucide-react";
import { formatDistanceToNow } from "date-fns";

interface Certificate {
  id: string;
  certificateNumber: string;
  title: string;
  description: string;
  recipientName: string;
  recipientEmail: string;
  issuerName: string;
  issuerTitle?: string;
  issuedAt: string;
  completionDate: string;
  completionTime: number;
  finalScore?: number;
  achievements: string[];
  verificationCode: string;
  tutorial: {
    id: string;
    title: string;
    description?: string;
    metadata: any;
  };
  template?: {
    id: string;
    name: string;
    layout: string;
    colorScheme: string;
  };
  metadata: any;
}

interface CertificateDisplayProps {
  certificate: Certificate;
  showActions?: boolean;
  compact?: boolean;
}

export default function CertificateDisplay({
  certificate,
  showActions = true,
  compact = false,
}: CertificateDisplayProps) {
  const { toast } = useToast();
  const [isPreviewOpen, setIsPreviewOpen] = useState(false);

  const formatDuration = (seconds: number) => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    
    if (hours > 0) {
      return `${hours}h ${minutes}m`;
    }
    return `${minutes}m`;
  };

  const getColorSchemeClasses = (colorScheme: string) => {
    switch (colorScheme) {
      case "blue":
        return "border-blue-200 bg-gradient-to-br from-blue-50 to-blue-100";
      case "green":
        return "border-green-200 bg-gradient-to-br from-green-50 to-green-100";
      case "purple":
        return "border-purple-200 bg-gradient-to-br from-purple-50 to-purple-100";
      case "gold":
        return "border-yellow-200 bg-gradient-to-br from-yellow-50 to-yellow-100";
      default:
        return "border-gray-200 bg-gradient-to-br from-gray-50 to-gray-100";
    }
  };

  const copyVerificationCode = async () => {
    try {
      await navigator.clipboard.writeText(certificate.verificationCode);
      toast({
        title: "Copied",
        description: "Verification code copied to clipboard",
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to copy verification code",
        variant: "destructive",
      });
    }
  };

  const shareVerificationLink = async () => {
    const verificationUrl = `${window.location.origin}/certificates/verify/${certificate.verificationCode}`;
    
    if (navigator.share) {
      try {
        await navigator.share({
          title: `Certificate Verification - ${certificate.title}`,
          text: `Verify this certificate for ${certificate.recipientName}`,
          url: verificationUrl,
        });
      } catch (error) {
        // Fallback to clipboard
        await navigator.clipboard.writeText(verificationUrl);
        toast({
          title: "Shared",
          description: "Verification link copied to clipboard",
        });
      }
    } else {
      await navigator.clipboard.writeText(verificationUrl);
      toast({
        title: "Shared",
        description: "Verification link copied to clipboard",
      });
    }
  };

  const downloadCertificate = () => {
    // This would trigger certificate PDF generation
    toast({
      title: "Download",
      description: "Certificate download will be available soon",
    });
  };

  if (compact) {
    return (
      <Card className="hover:shadow-md transition-shadow">
        <CardContent className="p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="p-2 rounded-full bg-primary/10">
                <Award className="h-5 w-5 text-primary" />
              </div>
              <div>
                <h3 className="font-medium">{certificate.title}</h3>
                <p className="text-sm text-muted-foreground">
                  {certificate.tutorial.title}
                </p>
                <p className="text-xs text-muted-foreground">
                  Issued {formatDistanceToNow(new Date(certificate.issuedAt), { addSuffix: true })}
                </p>
              </div>
            </div>
            
            <div className="flex items-center gap-2">
              <Badge variant="outline" className="text-xs">
                {certificate.tutorial.metadata?.difficulty || "N/A"}
              </Badge>
              {showActions && (
                <Dialog open={isPreviewOpen} onOpenChange={setIsPreviewOpen}>
                  <DialogTrigger asChild>
                    <Button variant="ghost" size="sm">
                      <Eye className="h-4 w-4" />
                    </Button>
                  </DialogTrigger>
                  <DialogContent className="max-w-4xl">
                    <DialogHeader>
                      <DialogTitle>Certificate Preview</DialogTitle>
                      <DialogDescription>
                        Certificate for {certificate.tutorial.title}
                      </DialogDescription>
                    </DialogHeader>
                    <CertificatePreview certificate={certificate} />
                  </DialogContent>
                </Dialog>
              )}
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="hover:shadow-lg transition-shadow">
      <CardHeader>
        <div className="flex items-start justify-between">
          <div className="flex items-center gap-3">
            <div className="p-3 rounded-full bg-primary/10">
              <Award className="h-6 w-6 text-primary" />
            </div>
            <div>
              <CardTitle className="text-lg">{certificate.title}</CardTitle>
              <CardDescription>{certificate.tutorial.title}</CardDescription>
            </div>
          </div>
          
          <Badge variant="secondary" className="text-xs">
            #{certificate.certificateNumber}
          </Badge>
        </div>
      </CardHeader>
      
      <CardContent className="space-y-4">
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
          <div className="flex items-center gap-2">
            <Calendar className="h-4 w-4 text-muted-foreground" />
            <div>
              <div className="font-medium">Completed</div>
              <div className="text-muted-foreground">
                {new Date(certificate.completionDate).toLocaleDateString()}
              </div>
            </div>
          </div>
          
          <div className="flex items-center gap-2">
            <Clock className="h-4 w-4 text-muted-foreground" />
            <div>
              <div className="font-medium">Duration</div>
              <div className="text-muted-foreground">
                {formatDuration(certificate.completionTime)}
              </div>
            </div>
          </div>
          
          {certificate.finalScore && (
            <div className="flex items-center gap-2">
              <Star className="h-4 w-4 text-muted-foreground" />
              <div>
                <div className="font-medium">Score</div>
                <div className="text-muted-foreground">
                  {certificate.finalScore.toFixed(1)}%
                </div>
              </div>
            </div>
          )}
          
          <div className="flex items-center gap-2">
            <Shield className="h-4 w-4 text-muted-foreground" />
            <div>
              <div className="font-medium">Verified</div>
              <div className="text-muted-foreground text-xs">
                {certificate.verificationCode.slice(0, 8)}...
              </div>
            </div>
          </div>
        </div>

        {certificate.achievements.length > 0 && (
          <div>
            <div className="text-sm font-medium mb-2">Achievements</div>
            <div className="flex flex-wrap gap-1">
              {certificate.achievements.map((achievement, index) => (
                <Badge key={index} variant="outline" className="text-xs">
                  {achievement}
                </Badge>
              ))}
            </div>
          </div>
        )}

        <div className="text-xs text-muted-foreground">
          <div>Issued by: {certificate.issuerName}</div>
          <div>
            Issued: {formatDistanceToNow(new Date(certificate.issuedAt), { addSuffix: true })}
          </div>
        </div>

        {showActions && (
          <div className="flex items-center gap-2 pt-2 border-t">
            <Dialog open={isPreviewOpen} onOpenChange={setIsPreviewOpen}>
              <DialogTrigger asChild>
                <Button variant="outline" size="sm" className="gap-1">
                  <Eye className="h-4 w-4" />
                  Preview
                </Button>
              </DialogTrigger>
              <DialogContent className="max-w-4xl">
                <DialogHeader>
                  <DialogTitle>Certificate Preview</DialogTitle>
                  <DialogDescription>
                    Certificate for {certificate.tutorial.title}
                  </DialogDescription>
                </DialogHeader>
                <CertificatePreview certificate={certificate} />
              </DialogContent>
            </Dialog>

            <Button variant="outline" size="sm" onClick={downloadCertificate} className="gap-1">
              <Download className="h-4 w-4" />
              Download
            </Button>

            <Button variant="outline" size="sm" onClick={shareVerificationLink} className="gap-1">
              <Share className="h-4 w-4" />
              Share
            </Button>

            <Button variant="outline" size="sm" onClick={copyVerificationCode} className="gap-1">
              <Copy className="h-4 w-4" />
              Copy Code
            </Button>

            <Button
              variant="outline"
              size="sm"
              onClick={() => window.open(`/certificates/verify/${certificate.verificationCode}`, '_blank')}
              className="gap-1"
            >
              <ExternalLink className="h-4 w-4" />
              Verify
            </Button>
          </div>
        )}
      </CardContent>
    </Card>
  );
}

function CertificatePreview({ certificate }: { certificate: Certificate }) {
  const colorScheme = certificate.template?.colorScheme || "blue";
  
  return (
    <div className={`p-8 border-2 rounded-lg ${getColorSchemeClasses(colorScheme)} min-h-[400px] flex flex-col justify-center items-center text-center space-y-6`}>
      <div className="space-y-2">
        <h1 className="text-3xl font-bold text-gray-800">{certificate.title}</h1>
        <div className="w-24 h-1 bg-primary mx-auto rounded"></div>
      </div>
      
      <div className="space-y-4 max-w-2xl">
        <p className="text-lg text-gray-700">{certificate.description}</p>
        
        <div className="text-sm text-gray-600 space-y-1">
          <div>Completed on {new Date(certificate.completionDate).toLocaleDateString()}</div>
          <div>Duration: {formatDuration(certificate.completionTime)}</div>
          {certificate.finalScore && (
            <div>Final Score: {certificate.finalScore.toFixed(1)}%</div>
          )}
        </div>
      </div>
      
      <div className="space-y-2">
        <div className="text-sm text-gray-600">
          Issued by: {certificate.issuerName}
          {certificate.issuerTitle && `, ${certificate.issuerTitle}`}
        </div>
        <div className="text-xs text-gray-500">
          Certificate #{certificate.certificateNumber}
        </div>
        <div className="text-xs text-gray-500">
          Verification Code: {certificate.verificationCode}
        </div>
      </div>
    </div>
  );
}

function getColorSchemeClasses(colorScheme: string) {
  switch (colorScheme) {
    case "blue":
      return "border-blue-200 bg-gradient-to-br from-blue-50 to-blue-100";
    case "green":
      return "border-green-200 bg-gradient-to-br from-green-50 to-green-100";
    case "purple":
      return "border-purple-200 bg-gradient-to-br from-purple-50 to-purple-100";
    case "gold":
      return "border-yellow-200 bg-gradient-to-br from-yellow-50 to-yellow-100";
    default:
      return "border-gray-200 bg-gradient-to-br from-gray-50 to-gray-100";
  }
}

function formatDuration(seconds: number) {
  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  
  if (hours > 0) {
    return `${hours}h ${minutes}m`;
  }
  return `${minutes}m`;
}
