"use client";

import React, { useState, useEffect, use<PERSON><PERSON>back } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { Checkbox } from "@/components/ui/checkbox";
import { Slider } from "@/components/ui/slider";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import {
  Search,
  Filter,
  Star,
  Clock,
  Users,
  Eye,
  TrendingUp,
  Sparkles,
  BookOpen,
  X,
  ChevronLeft,
  ChevronRight,
} from "lucide-react";
import { useDebounce } from "@/hooks/use-debounce";
import Link from "next/link";

interface SearchFilters {
  query: string;
  category: string;
  difficulty: string;
  language: string;
  tags: string[];
  minDuration: number;
  maxDuration: number;
  minRating: number;
  sortBy: string;
  sortOrder: string;
}

interface Tutorial {
  id: string;
  title: string;
  description?: string;
  language: string;
  tags?: string[];
  estimatedDuration?: number;
  thumbnail?: string;
  creator: {
    id: string;
    name: string;
    email: string;
    image?: string;
  };
  metadata: {
    category: string;
    difficulty: string;
    estimatedTime: number;
  };
  analytics: {
    views: number;
    completions: number;
    averageRating: number;
  };
  relevanceScore?: number;
  isRecommended?: boolean;
  recommendationType?: string;
}

interface SearchResult {
  tutorials: Tutorial[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    pages: number;
  };
  filters: {
    categories: Array<{ category: string; count: number }>;
    difficulties: Array<{ difficulty: string; count: number }>;
    languages: Array<{ language: string; count: number }>;
  };
}

export default function TutorialSearchAndDiscovery() {
  const [searchResults, setSearchResults] = useState<SearchResult | null>(null);
  const [recommendations, setRecommendations] = useState<Tutorial[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isLoadingRecommendations, setIsLoadingRecommendations] = useState(false);
  const [showFilters, setShowFilters] = useState(false);
  
  const [filters, setFilters] = useState<SearchFilters>({
    query: "",
    category: "",
    difficulty: "",
    language: "",
    tags: [],
    minDuration: 0,
    maxDuration: 120,
    minRating: 0,
    sortBy: "relevance",
    sortOrder: "desc",
  });

  const debouncedQuery = useDebounce(filters.query, 300);

  // Load recommendations on component mount
  useEffect(() => {
    loadRecommendations();
  }, []);

  // Perform search when query or filters change
  useEffect(() => {
    if (debouncedQuery || Object.values(filters).some(v => 
      Array.isArray(v) ? v.length > 0 : v !== "" && v !== 0
    )) {
      performSearch();
    } else {
      setSearchResults(null);
    }
  }, [debouncedQuery, filters.category, filters.difficulty, filters.language, filters.tags, filters.sortBy]);

  const loadRecommendations = async () => {
    try {
      setIsLoadingRecommendations(true);
      const response = await fetch("/api/tutorials/recommendations?limit=6");
      
      if (response.ok) {
        const data = await response.json();
        setRecommendations(data.recommendations);
      }
    } catch (error) {
      console.error("Error loading recommendations:", error);
    } finally {
      setIsLoadingRecommendations(false);
    }
  };

  const performSearch = async (page = 1) => {
    try {
      setIsLoading(true);
      
      const searchParams = new URLSearchParams({
        ...(filters.query && { query: filters.query }),
        ...(filters.category && { category: filters.category }),
        ...(filters.difficulty && { difficulty: filters.difficulty }),
        ...(filters.language && { language: filters.language }),
        ...(filters.tags.length > 0 && { tags: filters.tags.join(",") }),
        ...(filters.minDuration > 0 && { minDuration: filters.minDuration.toString() }),
        ...(filters.maxDuration < 120 && { maxDuration: filters.maxDuration.toString() }),
        ...(filters.minRating > 0 && { minRating: filters.minRating.toString() }),
        sortBy: filters.sortBy,
        sortOrder: filters.sortOrder,
        page: page.toString(),
      });

      const response = await fetch(`/api/tutorials/search?${searchParams}`);
      
      if (response.ok) {
        const data = await response.json();
        setSearchResults(data);
      }
    } catch (error) {
      console.error("Error performing search:", error);
    } finally {
      setIsLoading(false);
    }
  };

  const updateFilter = useCallback((key: keyof SearchFilters, value: any) => {
    setFilters(prev => ({ ...prev, [key]: value }));
  }, []);

  const clearFilters = () => {
    setFilters({
      query: "",
      category: "",
      difficulty: "",
      language: "",
      tags: [],
      minDuration: 0,
      maxDuration: 120,
      minRating: 0,
      sortBy: "relevance",
      sortOrder: "desc",
    });
  };

  const addTag = (tag: string) => {
    if (!filters.tags.includes(tag)) {
      updateFilter("tags", [...filters.tags, tag]);
    }
  };

  const removeTag = (tag: string) => {
    updateFilter("tags", filters.tags.filter(t => t !== tag));
  };

  const TutorialCard = ({ tutorial }: { tutorial: Tutorial }) => (
    <Card className="hover:shadow-md transition-shadow">
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <div className="flex-1">
            <div className="flex items-center gap-2 mb-2">
              <Link 
                href={`/tutorials/${tutorial.id}`}
                className="font-semibold hover:text-primary line-clamp-2"
              >
                {tutorial.title}
              </Link>
              {tutorial.isRecommended && (
                <Badge variant="secondary" className="text-xs">
                  <Sparkles className="h-3 w-3 mr-1" />
                  Recommended
                </Badge>
              )}
            </div>
            
            <p className="text-sm text-muted-foreground line-clamp-2 mb-3">
              {tutorial.description}
            </p>

            <div className="flex items-center gap-4 text-xs text-muted-foreground">
              <div className="flex items-center gap-1">
                <Avatar className="h-4 w-4">
                  <AvatarImage src={tutorial.creator.image} />
                  <AvatarFallback className="text-xs">
                    {tutorial.creator.name?.charAt(0) || tutorial.creator.email.charAt(0)}
                  </AvatarFallback>
                </Avatar>
                <span>{tutorial.creator.name || tutorial.creator.email}</span>
              </div>
              
              <div className="flex items-center gap-1">
                <Clock className="h-3 w-3" />
                <span>{tutorial.metadata.estimatedTime}m</span>
              </div>
              
              <div className="flex items-center gap-1">
                <Eye className="h-3 w-3" />
                <span>{tutorial.analytics.views}</span>
              </div>
              
              {tutorial.analytics.averageRating > 0 && (
                <div className="flex items-center gap-1">
                  <Star className="h-3 w-3 fill-yellow-400 text-yellow-400" />
                  <span>{tutorial.analytics.averageRating.toFixed(1)}</span>
                </div>
              )}
            </div>
          </div>
        </div>
      </CardHeader>
      
      <CardContent className="pt-0">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Badge variant="outline" className="text-xs">
              {tutorial.metadata.category}
            </Badge>
            <Badge variant="outline" className="text-xs">
              {tutorial.metadata.difficulty}
            </Badge>
          </div>
          
          {tutorial.tags && tutorial.tags.length > 0 && (
            <div className="flex gap-1">
              {tutorial.tags.slice(0, 2).map(tag => (
                <Badge
                  key={tag}
                  variant="secondary"
                  className="text-xs cursor-pointer"
                  onClick={() => addTag(tag)}
                >
                  {tag}
                </Badge>
              ))}
              {tutorial.tags.length > 2 && (
                <Badge variant="secondary" className="text-xs">
                  +{tutorial.tags.length - 2}
                </Badge>
              )}
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );

  return (
    <div className="space-y-6">
      {/* Search Header */}
      <div className="flex items-center gap-4">
        <div className="flex-1 relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search tutorials..."
            value={filters.query}
            onChange={(e) => updateFilter("query", e.target.value)}
            className="pl-10"
          />
        </div>
        
        <Button
          variant="outline"
          onClick={() => setShowFilters(!showFilters)}
          className="gap-2"
        >
          <Filter className="h-4 w-4" />
          Filters
        </Button>

        <Select value={filters.sortBy} onValueChange={(value) => updateFilter("sortBy", value)}>
          <SelectTrigger className="w-40">
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="relevance">Relevance</SelectItem>
            <SelectItem value="created">Newest</SelectItem>
            <SelectItem value="rating">Rating</SelectItem>
            <SelectItem value="popularity">Popular</SelectItem>
            <SelectItem value="duration">Duration</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {/* Active Filters */}
      {(filters.category || filters.difficulty || filters.language || filters.tags.length > 0) && (
        <div className="flex items-center gap-2 flex-wrap">
          <span className="text-sm text-muted-foreground">Active filters:</span>
          
          {filters.category && (
            <Badge variant="secondary" className="gap-1">
              Category: {filters.category}
              <X className="h-3 w-3 cursor-pointer" onClick={() => updateFilter("category", "")} />
            </Badge>
          )}
          
          {filters.difficulty && (
            <Badge variant="secondary" className="gap-1">
              Difficulty: {filters.difficulty}
              <X className="h-3 w-3 cursor-pointer" onClick={() => updateFilter("difficulty", "")} />
            </Badge>
          )}
          
          {filters.language && (
            <Badge variant="secondary" className="gap-1">
              Language: {filters.language}
              <X className="h-3 w-3 cursor-pointer" onClick={() => updateFilter("language", "")} />
            </Badge>
          )}
          
          {filters.tags.map(tag => (
            <Badge key={tag} variant="secondary" className="gap-1">
              {tag}
              <X className="h-3 w-3 cursor-pointer" onClick={() => removeTag(tag)} />
            </Badge>
          ))}
          
          <Button variant="ghost" size="sm" onClick={clearFilters}>
            Clear all
          </Button>
        </div>
      )}

      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
        {/* Filters Sidebar */}
        {showFilters && (
          <div className="lg:col-span-1">
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Filters</CardTitle>
              </CardHeader>
              <CardContent>
                <Accordion type="single" collapsible className="w-full">
                  <AccordionItem value="category">
                    <AccordionTrigger>Category</AccordionTrigger>
                    <AccordionContent>
                      <div className="space-y-2">
                        {searchResults?.filters.categories.map(({ category, count }) => (
                          <div key={category} className="flex items-center space-x-2">
                            <Checkbox
                              id={`category-${category}`}
                              checked={filters.category === category}
                              onCheckedChange={(checked) => 
                                updateFilter("category", checked ? category : "")
                              }
                            />
                            <label
                              htmlFor={`category-${category}`}
                              className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 flex-1"
                            >
                              {category} ({count})
                            </label>
                          </div>
                        ))}
                      </div>
                    </AccordionContent>
                  </AccordionItem>

                  <AccordionItem value="difficulty">
                    <AccordionTrigger>Difficulty</AccordionTrigger>
                    <AccordionContent>
                      <div className="space-y-2">
                        {["beginner", "intermediate", "advanced"].map(difficulty => (
                          <div key={difficulty} className="flex items-center space-x-2">
                            <Checkbox
                              id={`difficulty-${difficulty}`}
                              checked={filters.difficulty === difficulty}
                              onCheckedChange={(checked) => 
                                updateFilter("difficulty", checked ? difficulty : "")
                              }
                            />
                            <label
                              htmlFor={`difficulty-${difficulty}`}
                              className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 capitalize"
                            >
                              {difficulty}
                            </label>
                          </div>
                        ))}
                      </div>
                    </AccordionContent>
                  </AccordionItem>

                  <AccordionItem value="duration">
                    <AccordionTrigger>Duration (minutes)</AccordionTrigger>
                    <AccordionContent>
                      <div className="space-y-4">
                        <div>
                          <label className="text-sm font-medium">Min: {filters.minDuration}m</label>
                          <Slider
                            value={[filters.minDuration]}
                            onValueChange={([value]) => updateFilter("minDuration", value)}
                            max={120}
                            step={5}
                            className="mt-2"
                          />
                        </div>
                        <div>
                          <label className="text-sm font-medium">Max: {filters.maxDuration}m</label>
                          <Slider
                            value={[filters.maxDuration]}
                            onValueChange={([value]) => updateFilter("maxDuration", value)}
                            max={120}
                            step={5}
                            className="mt-2"
                          />
                        </div>
                      </div>
                    </AccordionContent>
                  </AccordionItem>

                  <AccordionItem value="rating">
                    <AccordionTrigger>Minimum Rating</AccordionTrigger>
                    <AccordionContent>
                      <div className="space-y-2">
                        {[0, 1, 2, 3, 4, 5].map(rating => (
                          <div key={rating} className="flex items-center space-x-2">
                            <Checkbox
                              id={`rating-${rating}`}
                              checked={filters.minRating === rating}
                              onCheckedChange={(checked) => 
                                updateFilter("minRating", checked ? rating : 0)
                              }
                            />
                            <label
                              htmlFor={`rating-${rating}`}
                              className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 flex items-center gap-1"
                            >
                              {rating === 0 ? "Any" : (
                                <>
                                  {rating}
                                  <Star className="h-3 w-3 fill-yellow-400 text-yellow-400" />
                                  & up
                                </>
                              )}
                            </label>
                          </div>
                        ))}
                      </div>
                    </AccordionContent>
                  </AccordionItem>
                </Accordion>
              </CardContent>
            </Card>
          </div>
        )}

        {/* Main Content */}
        <div className={showFilters ? "lg:col-span-3" : "lg:col-span-4"}>
          {/* Recommendations Section */}
          {!searchResults && recommendations.length > 0 && (
            <div className="mb-8">
              <div className="flex items-center gap-2 mb-4">
                <TrendingUp className="h-5 w-5" />
                <h2 className="text-xl font-semibold">Recommended for You</h2>
              </div>
              
              {isLoadingRecommendations ? (
                <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-4">
                  {[...Array(6)].map((_, i) => (
                    <Card key={i} className="animate-pulse">
                      <CardHeader>
                        <div className="h-4 bg-muted rounded w-3/4"></div>
                        <div className="h-3 bg-muted rounded w-full"></div>
                      </CardHeader>
                      <CardContent>
                        <div className="h-3 bg-muted rounded w-1/2"></div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              ) : (
                <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-4">
                  {recommendations.map(tutorial => (
                    <TutorialCard key={tutorial.id} tutorial={tutorial} />
                  ))}
                </div>
              )}
            </div>
          )}

          {/* Search Results */}
          {searchResults && (
            <div>
              <div className="flex items-center justify-between mb-4">
                <h2 className="text-xl font-semibold">
                  Search Results ({searchResults.pagination.total})
                </h2>
              </div>

              {isLoading ? (
                <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-4">
                  {[...Array(6)].map((_, i) => (
                    <Card key={i} className="animate-pulse">
                      <CardHeader>
                        <div className="h-4 bg-muted rounded w-3/4"></div>
                        <div className="h-3 bg-muted rounded w-full"></div>
                      </CardHeader>
                      <CardContent>
                        <div className="h-3 bg-muted rounded w-1/2"></div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              ) : searchResults.tutorials.length > 0 ? (
                <>
                  <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-4 mb-6">
                    {searchResults.tutorials.map(tutorial => (
                      <TutorialCard key={tutorial.id} tutorial={tutorial} />
                    ))}
                  </div>

                  {/* Pagination */}
                  {searchResults.pagination.pages > 1 && (
                    <div className="flex items-center justify-center gap-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => performSearch(searchResults.pagination.page - 1)}
                        disabled={searchResults.pagination.page === 1}
                      >
                        <ChevronLeft className="h-4 w-4" />
                        Previous
                      </Button>
                      
                      <span className="text-sm text-muted-foreground">
                        Page {searchResults.pagination.page} of {searchResults.pagination.pages}
                      </span>
                      
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => performSearch(searchResults.pagination.page + 1)}
                        disabled={searchResults.pagination.page === searchResults.pagination.pages}
                      >
                        Next
                        <ChevronRight className="h-4 w-4" />
                      </Button>
                    </div>
                  )}
                </>
              ) : (
                <div className="text-center py-12">
                  <BookOpen className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
                  <h3 className="text-lg font-medium mb-2">No tutorials found</h3>
                  <p className="text-muted-foreground mb-4">
                    Try adjusting your search terms or filters
                  </p>
                  <Button onClick={clearFilters}>Clear filters</Button>
                </div>
              )}
            </div>
          )}

          {/* Empty State */}
          {!searchResults && recommendations.length === 0 && !isLoadingRecommendations && (
            <div className="text-center py-12">
              <Search className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
              <h3 className="text-lg font-medium mb-2">Discover Tutorials</h3>
              <p className="text-muted-foreground">
                Search for tutorials or browse our recommendations
              </p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
