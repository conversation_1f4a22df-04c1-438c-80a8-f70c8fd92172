import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { prisma } from "@/lib/database";
import { AdaptiveLearningService } from "@/lib/adaptive-learning";
import { z } from "zod";

const updateProfileSchema = z.object({
  learningStyle: z.enum(["visual", "auditory", "kinesthetic", "mixed"]).optional(),
  preferredPace: z.enum(["slow", "medium", "fast"]).optional(),
  difficultyLevel: z.enum(["beginner", "intermediate", "advanced"]).optional(),
  attentionSpan: z.number().min(60).max(3600).optional(),
  preferredHints: z.boolean().optional(),
  skipOptionalSteps: z.boolean().optional(),
  voiceEnabled: z.boolean().optional(),
  adaptiveEnabled: z.boolean().optional(),
  sessionDuration: z.number().min(300).max(7200).optional(),
  breakFrequency: z.number().min(300).max(3600).optional(),
});

// GET /api/users/[userId]/learning-profile - Get user learning profile
export async function GET(
  request: NextRequest,
  { params }: { params: { userId: string } }
) {
  try {
    const session = await getServerSession();

    if (!session?.user?.email) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const user = await prisma.user.findUnique({
      where: { email: session.user.email },
    });

    if (!user) {
      return NextResponse.json({ error: "User not found" }, { status: 404 });
    }

    const targetUserId = params.userId;

    // Check if user can access this profile (self or admin)
    if (user.id !== targetUserId && user.role !== "ADMIN" && user.role !== "SUPER_ADMIN") {
      return NextResponse.json({ error: "Forbidden" }, { status: 403 });
    }

    // Get or create learning profile
    const profile = await AdaptiveLearningService.getUserLearningProfile(targetUserId);

    if (!profile) {
      return NextResponse.json({ error: "Profile not found" }, { status: 404 });
    }

    return NextResponse.json({ profile });
  } catch (error) {
    console.error("Error fetching learning profile:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

// PUT /api/users/[userId]/learning-profile - Update user learning profile
export async function PUT(
  request: NextRequest,
  { params }: { params: { userId: string } }
) {
  try {
    const session = await getServerSession();

    if (!session?.user?.email) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const user = await prisma.user.findUnique({
      where: { email: session.user.email },
    });

    if (!user) {
      return NextResponse.json({ error: "User not found" }, { status: 404 });
    }

    const targetUserId = params.userId;

    // Check if user can update this profile (self only)
    if (user.id !== targetUserId) {
      return NextResponse.json({ error: "Forbidden" }, { status: 403 });
    }

    const body = await request.json();
    const validatedData = updateProfileSchema.parse(body);

    // Get current profile
    const currentProfile = await AdaptiveLearningService.getUserLearningProfile(targetUserId);
    if (!currentProfile) {
      return NextResponse.json({ error: "Profile not found" }, { status: 404 });
    }

    // Update profile
    const updatedProfile = await prisma.userLearningProfile.update({
      where: { userId: targetUserId },
      data: {
        ...validatedData,
        lastUpdated: new Date(),
      },
    });

    // Log the manual update
    await prisma.analytics.create({
      data: {
        userId: targetUserId,
        action: "learning_profile_updated",
        metadata: {
          changes: validatedData,
          updateType: "manual",
        },
      },
    });

    return NextResponse.json({
      message: "Learning profile updated successfully",
      profile: updatedProfile,
    });
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Invalid request data", details: error.errors },
        { status: 400 }
      );
    }

    console.error("Error updating learning profile:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

// POST /api/users/[userId]/learning-profile/session - Update profile based on session data
export async function POST(
  request: NextRequest,
  { params }: { params: { userId: string } }
) {
  try {
    const session = await getServerSession();

    if (!session?.user?.email) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const user = await prisma.user.findUnique({
      where: { email: session.user.email },
    });

    if (!user) {
      return NextResponse.json({ error: "User not found" }, { status: 404 });
    }

    const targetUserId = params.userId;

    // Check if user can update this profile (self only)
    if (user.id !== targetUserId) {
      return NextResponse.json({ error: "Forbidden" }, { status: 403 });
    }

    const body = await request.json();
    const sessionData = z.object({
      tutorialId: z.string(),
      completionTime: z.number().min(0),
      errorRate: z.number().min(0).max(1),
      retryRate: z.number().min(0).max(1),
      hintsUsed: z.number().min(0),
      stepsSkipped: z.number().min(0),
      satisfactionRating: z.number().min(1).max(5).optional(),
    }).parse(body);

    // Update learning profile based on session performance
    await AdaptiveLearningService.updateLearningProfile(targetUserId, sessionData);

    // Get updated profile
    const updatedProfile = await AdaptiveLearningService.getUserLearningProfile(targetUserId);

    return NextResponse.json({
      message: "Learning profile updated based on session data",
      profile: updatedProfile,
    });
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Invalid session data", details: error.errors },
        { status: 400 }
      );
    }

    console.error("Error updating learning profile from session:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
