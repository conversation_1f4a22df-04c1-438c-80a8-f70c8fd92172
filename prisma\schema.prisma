// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model Account {
  id                String  @id @default(cuid())
  userId            String
  type              String
  provider          String
  providerAccountId String
  refresh_token     String? @db.Text
  access_token      String? @db.Text
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String? @db.Text
  session_state     String?

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([provider, providerAccountId])
}

model Session {
  id           String   @id @default(cuid())
  sessionToken String   @unique
  userId       String
  expires      DateTime
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)
}

model User {
  id            String    @id @default(cuid())
  name          String?
  email         String    @unique
  emailVerified DateTime?
  image         String?
  password      String?
  role          UserRole  @default(USER)
  subscription  String    @default("free")
  settings      Json      @default("{}")
  isActive      Boolean   @default(true)
  lastLoginAt   DateTime?
  loginAttempts Int       @default(0)
  lockedUntil   DateTime?
  createdAt     DateTime  @default(now())
  updatedAt     DateTime  @updatedAt

  accounts        Account[]
  sessions        Session[]
  tutorials       Tutorial[]
  editedTutorials Tutorial[] @relation("TutorialLastEditor")
  tutorialVersions TutorialVersion[]
  tutorialCollaborations TutorialCollaborator[]
  addedCollaborators TutorialCollaborator[] @relation("TutorialCollaboratorAdder")
  tutorialTemplates TutorialTemplate[]
  learningProfile UserLearningProfile?
  learningRecommendations LearningRecommendation[]
  adaptiveSessions AdaptiveTutorialSession[]
  certificates    Certificate[]
  revokedCertificates Certificate[] @relation("CertificateRevoker")
  certificateTemplates CertificateTemplate[]
  achievements    Achievement[]
  userAchievements UserAchievement[]
  aiUsage         AIUsage[]
  analytics       Analytics[]
  permissions     UserPermission[]
  auditLogs       AuditLog[]
  grantedPermissions UserPermission[] @relation("GrantedPermissions")
  tutorialProgress TutorialProgress[]

  @@index([email])
  @@index([role])
  @@index([isActive])
  @@map("users")
}

model VerificationToken {
  identifier String
  token      String   @unique
  expires    DateTime

  @@unique([identifier, token])
}

model Tutorial {
  id          String   @id @default(cuid())
  title       String
  description String?
  steps       Json
  language    String   @default("en")
  isActive    Boolean  @default(true)
  metadata    Json     @default("{}")
  createdBy   String
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Versioning fields
  parentId    String?  // For version history
  version     String   @default("1.0.0")
  status      String   @default("draft") // draft, review, published, archived, deprecated
  publishedAt DateTime?
  lastEditedBy String?
  tags        String[] @default([])
  thumbnail   String?
  estimatedDuration Int?

  creator      User        @relation(fields: [createdBy], references: [id])
  lastEditor   User?       @relation("TutorialLastEditor", fields: [lastEditedBy], references: [id])
  parent       Tutorial?   @relation("TutorialVersions", fields: [parentId], references: [id])
  versions     Tutorial[]  @relation("TutorialVersions")
  analytics    Analytics[]
  progress     TutorialProgress[]
  collaborators TutorialCollaborator[]
  versionHistory TutorialVersion[]
  recommendations LearningRecommendation[]
  adaptiveSessions AdaptiveTutorialSession[]
  certificates Certificate[]
  userAchievements UserAchievement[]

  @@index([parentId])
  @@index([status])
  @@index([version])
  @@map("tutorials")
}

model TutorialProgress {
  id          String    @id @default(cuid())
  tutorialId  String
  userId      String
  currentStep Int       @default(0)
  progress    Int       @default(0)
  completed   Boolean   @default(false)
  startedAt   DateTime  @default(now())
  completedAt DateTime?
  timeSpent   Int       @default(0)

  tutorial    Tutorial  @relation(fields: [tutorialId], references: [id], onDelete: Cascade)
  user        User      @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([tutorialId, userId])
  @@map("tutorial_progress")
}

model TutorialVersion {
  id          String   @id @default(cuid())
  tutorialId  String
  version     String
  title       String
  description String?
  steps       Json
  metadata    Json     @default("{}")
  createdBy   String
  createdAt   DateTime @default(now())
  isActive    Boolean  @default(false)
  changeNotes String?

  tutorial    Tutorial @relation(fields: [tutorialId], references: [id], onDelete: Cascade)
  creator     User     @relation(fields: [createdBy], references: [id])

  @@unique([tutorialId, version])
  @@index([tutorialId])
  @@index([version])
  @@map("tutorial_versions")
}

model TutorialCollaborator {
  id          String   @id @default(cuid())
  tutorialId  String
  userId      String
  role        String   // owner, editor, reviewer, viewer
  permissions Json     @default("[]")
  addedAt     DateTime @default(now())
  addedBy     String

  tutorial    Tutorial @relation(fields: [tutorialId], references: [id], onDelete: Cascade)
  user        User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  addedByUser User     @relation("TutorialCollaboratorAdder", fields: [addedBy], references: [id])

  @@unique([tutorialId, userId])
  @@index([tutorialId])
  @@index([userId])
  @@map("tutorial_collaborators")
}

model TutorialTemplate {
  id          String   @id @default(cuid())
  name        String
  description String
  category    String
  steps       Json
  metadata    Json     @default("{}")
  isPublic    Boolean  @default(false)
  createdBy   String
  createdAt   DateTime @default(now())
  usageCount  Int      @default(0)

  creator     User     @relation(fields: [createdBy], references: [id])

  @@index([category])
  @@index([isPublic])
  @@map("tutorial_templates")
}

model UserLearningProfile {
  id                String   @id @default(cuid())
  userId            String   @unique
  learningStyle     String   @default("visual") // visual, auditory, kinesthetic, mixed
  preferredPace     String   @default("medium") // slow, medium, fast
  difficultyLevel   String   @default("beginner") // beginner, intermediate, advanced
  attentionSpan     Int      @default(300) // seconds
  preferredHints    Boolean  @default(true)
  skipOptionalSteps Boolean  @default(false)
  voiceEnabled      Boolean  @default(true)
  adaptiveEnabled   Boolean  @default(true)

  // Learning metrics
  averageCompletionTime Int @default(0)
  averageErrorRate      Float @default(0.0)
  averageRetryRate      Float @default(0.0)
  preferredCategories   String[] @default([])
  strongTopics          String[] @default([])
  weakTopics            String[] @default([])

  // Behavioral patterns
  peakLearningHours     String[] @default([]) // ["09:00", "14:00", "20:00"]
  sessionDuration       Int @default(1800) // preferred session length in seconds
  breakFrequency        Int @default(900) // break reminder frequency in seconds

  // Adaptation history
  adaptationHistory     Json @default("[]")
  lastUpdated          DateTime @updatedAt
  createdAt            DateTime @default(now())

  user                 User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("user_learning_profiles")
}

model LearningRecommendation {
  id            String   @id @default(cuid())
  userId        String
  tutorialId    String?
  type          String   // tutorial_suggestion, difficulty_adjustment, pace_change, hint_frequency
  title         String
  description   String
  confidence    Float    @default(0.0) // 0.0 to 1.0
  reasoning     String
  metadata      Json     @default("{}")
  isAccepted    Boolean?
  acceptedAt    DateTime?
  createdAt     DateTime @default(now())
  expiresAt     DateTime?

  user          User @relation(fields: [userId], references: [id], onDelete: Cascade)
  tutorial      Tutorial? @relation(fields: [tutorialId], references: [id], onDelete: Cascade)

  @@index([userId])
  @@index([tutorialId])
  @@index([type])
  @@index([createdAt])
  @@map("learning_recommendations")
}

model AdaptiveTutorialSession {
  id                    String   @id @default(cuid())
  userId                String
  tutorialId            String
  sessionId             String   @unique
  adaptations           Json     @default("[]")
  originalDifficulty    String
  adaptedDifficulty     String?
  originalPace          String
  adaptedPace           String?
  hintsProvided         Int      @default(0)
  errorsEncountered     Int      @default(0)
  retriesUsed           Int      @default(0)
  stepsSkipped          Int      @default(0)
  completionTime        Int?
  satisfactionRating    Int?
  adaptationEffectiveness Float?
  createdAt             DateTime @default(now())
  completedAt           DateTime?

  user                  User @relation(fields: [userId], references: [id], onDelete: Cascade)
  tutorial              Tutorial @relation(fields: [tutorialId], references: [id], onDelete: Cascade)

  @@index([userId])
  @@index([tutorialId])
  @@index([sessionId])
  @@map("adaptive_tutorial_sessions")
}

model Certificate {
  id                String   @id @default(cuid())
  userId            String
  tutorialId        String
  certificateNumber String   @unique
  title             String
  description       String?
  templateId        String?
  issuedAt          DateTime @default(now())
  expiresAt         DateTime?
  isRevoked         Boolean  @default(false)
  revokedAt         DateTime?
  revokedBy         String?
  verificationCode  String   @unique
  metadata          Json     @default("{}")

  // Certificate data
  recipientName     String
  recipientEmail    String
  issuerName        String
  issuerTitle       String?
  completionDate    DateTime
  completionTime    Int      // in seconds
  finalScore        Float?
  achievements      String[] @default([])

  user              User @relation(fields: [userId], references: [id], onDelete: Cascade)
  tutorial          Tutorial @relation(fields: [tutorialId], references: [id], onDelete: Cascade)
  template          CertificateTemplate? @relation(fields: [templateId], references: [id])
  revokedByUser     User? @relation("CertificateRevoker", fields: [revokedBy], references: [id])

  @@index([userId])
  @@index([tutorialId])
  @@index([certificateNumber])
  @@index([verificationCode])
  @@index([issuedAt])
  @@map("certificates")
}

model CertificateTemplate {
  id            String   @id @default(cuid())
  name          String
  description   String?
  category      String   @default("general")
  isDefault     Boolean  @default(false)
  isActive      Boolean  @default(true)

  // Template design
  layout        String   @default("standard") // standard, modern, classic, minimal
  colorScheme   String   @default("blue") // blue, green, purple, gold, custom
  logoUrl       String?
  backgroundUrl String?
  borderStyle   String   @default("simple") // simple, decorative, none

  // Template content
  titleTemplate String   @default("Certificate of Completion")
  bodyTemplate  String   @default("This certifies that {recipientName} has successfully completed the tutorial {tutorialTitle}")
  footerTemplate String? @default("Issued on {completionDate}")

  // Customization options
  showLogo      Boolean  @default(true)
  showDate      Boolean  @default(true)
  showScore     Boolean  @default(false)
  showDuration  Boolean  @default(false)
  showSignature Boolean  @default(true)

  // Template metadata
  createdBy     String
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt
  usageCount    Int      @default(0)

  creator       User @relation(fields: [createdBy], references: [id])
  certificates  Certificate[]

  @@index([category])
  @@index([isActive])
  @@index([isDefault])
  @@map("certificate_templates")
}

model Achievement {
  id            String   @id @default(cuid())
  name          String
  description   String
  category      String   @default("completion")
  type          String   // completion, speed, accuracy, streak, milestone
  iconUrl       String?
  badgeColor    String   @default("blue")

  // Achievement criteria
  criteria      Json     @default("{}")
  points        Int      @default(0)
  rarity        String   @default("common") // common, uncommon, rare, epic, legendary

  // Achievement metadata
  isActive      Boolean  @default(true)
  createdBy     String
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt

  creator       User @relation(fields: [createdBy], references: [id])
  userAchievements UserAchievement[]

  @@index([category])
  @@index([type])
  @@index([isActive])
  @@map("achievements")
}

model UserAchievement {
  id            String   @id @default(cuid())
  userId        String
  achievementId String
  tutorialId    String?
  earnedAt      DateTime @default(now())
  progress      Float    @default(100.0) // percentage of completion
  metadata      Json     @default("{}")

  user          User @relation(fields: [userId], references: [id], onDelete: Cascade)
  achievement   Achievement @relation(fields: [achievementId], references: [id], onDelete: Cascade)
  tutorial      Tutorial? @relation(fields: [tutorialId], references: [id], onDelete: Cascade)

  @@unique([userId, achievementId, tutorialId])
  @@index([userId])
  @@index([achievementId])
  @@index([earnedAt])
  @@map("user_achievements")
}

model AIUsage {
  id          String        @id @default(cuid())
  userId      String
  provider    AIProvider
  model       String
  tokens      Int
  cost        Float
  requestType AIRequestType
  metadata    Json          @default("{}")
  timestamp   DateTime      @default(now())

  user User @relation(fields: [userId], references: [id])

  @@map("ai_usage")
}

model Analytics {
  id         String   @id @default(cuid())
  userId     String
  tutorialId String?
  action     String
  metadata   Json     @default("{}")
  timestamp  DateTime @default(now())

  user     User      @relation(fields: [userId], references: [id])
  tutorial Tutorial? @relation(fields: [tutorialId], references: [id])

  @@map("analytics")
}

model UserPermission {
  id         String     @id @default(cuid())
  userId     String
  permission Permission
  grantedBy  String
  grantedAt  DateTime   @default(now())
  expiresAt  DateTime?
  
  user    User @relation(fields: [userId], references: [id], onDelete: Cascade)
  granter User @relation("GrantedPermissions", fields: [grantedBy], references: [id])
  
  @@unique([userId, permission])
  @@map("user_permissions")
}

model AuditLog {
  id        String   @id @default(cuid())
  userId    String?
  action    String
  resource  String
  resourceId String?
  oldValues Json?
  newValues Json?
  metadata  Json     @default("{}")
  ipAddress String?
  userAgent String?
  timestamp DateTime @default(now())
  
  user User? @relation(fields: [userId], references: [id])
  
  @@index([userId])
  @@index([action])
  @@index([resource])
  @@index([timestamp])
  @@map("audit_logs")
}

model RolePermission {
  id         String     @id @default(cuid())
  role       UserRole
  permission Permission
  createdAt  DateTime   @default(now())
  
  @@unique([role, permission])
  @@map("role_permissions")
}

enum UserRole {
  USER
  MODERATOR
  ADMIN
  SUPER_ADMIN
}

enum Permission {
  // User management
  USER_READ
  USER_WRITE
  USER_DELETE
  
  // Tutorial management
  TUTORIAL_READ
  TUTORIAL_WRITE
  TUTORIAL_DELETE
  TUTORIAL_PUBLISH
  
  // Analytics
  ANALYTICS_READ
  ANALYTICS_EXPORT
  
  // AI Usage
  AI_USAGE_READ
  AI_USAGE_MANAGE
  
  // System administration
  SYSTEM_CONFIG
  AUDIT_LOG_READ
  
  // Billing
  BILLING_READ
  BILLING_WRITE
}

enum AIProvider {
  openai
  anthropic
  google
  openrouter
  groq
}

enum AIRequestType {
  explanation
  tutorial_generation
  chat
  translation
  summarization
}
