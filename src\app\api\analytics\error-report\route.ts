/**
 * Error Report Analytics API
 * Handles individual error reports from browser extension
 */

import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { prisma } from '@/lib/database';
import { rateLimit } from '@/lib/rate-limit';

// Rate limiting for error reports
const limiter = rateLimit({
  interval: 60 * 1000, // 1 minute
  uniqueTokenPerInterval: 1000, // limit it to 1000 requests per minute
});

const ErrorReportSchema = z.object({
  extensionId: z.string(),
  error: z.object({
    id: z.string(),
    timestamp: z.number(),
    type: z.string(),
    message: z.string(),
    stack: z.string().optional(),
    filename: z.string().optional(),
    lineno: z.number().optional(),
    colno: z.number().optional(),
    severity: z.enum(['critical', 'high', 'medium', 'low', 'info']),
    category: z.string(),
    context: z.record(z.any()),
    userAgent: z.string(),
    url: z.string(),
  }),
  timestamp: z.string(),
});

export async function POST(request: NextRequest) {
  try {
    // Rate limiting
    const ip = request.ip ?? '127.0.0.1';
    const { success } = await limiter.check(50, ip); // 50 requests per minute per IP
    
    if (!success) {
      return NextResponse.json(
        { error: 'Too many requests' },
        { status: 429 }
      );
    }

    const body = await request.json();
    const validatedData = ErrorReportSchema.parse(body);

    // Store error report
    await prisma.analytics.create({
      data: {
        userId: 'system', // System-generated event
        action: 'extension_error_report',
        metadata: {
          extensionId: validatedData.extensionId,
          errorId: validatedData.error.id,
          errorType: validatedData.error.type,
          errorMessage: validatedData.error.message,
          errorSeverity: validatedData.error.severity,
          errorCategory: validatedData.error.category,
          errorStack: validatedData.error.stack,
          errorContext: validatedData.error.context,
          userAgent: validatedData.error.userAgent,
          url: validatedData.error.url,
          timestamp: validatedData.timestamp,
        },
      },
    });

    // Handle critical errors
    if (validatedData.error.severity === 'critical') {
      await handleCriticalError(validatedData);
    }

    // Check for error patterns
    await checkErrorPatterns(validatedData);

    return NextResponse.json({ success: true });

  } catch (error) {
    console.error('Error report analytics error:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid request format', details: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

async function handleCriticalError(errorData: any) {
  try {
    // Log critical errors for immediate attention
    console.error('Critical extension error received:', {
      extensionId: errorData.extensionId,
      type: errorData.error.type,
      message: errorData.error.message,
      category: errorData.error.category,
    });

    // Create audit log entry for critical errors
    await prisma.auditLog.create({
      data: {
        action: 'critical_extension_error',
        resource: 'extension',
        resourceId: errorData.extensionId,
        metadata: {
          errorId: errorData.error.id,
          errorType: errorData.error.type,
          errorMessage: errorData.error.message,
          errorCategory: errorData.error.category,
          errorContext: errorData.error.context,
          userAgent: errorData.error.userAgent,
        },
        ipAddress: '127.0.0.1', // Extension errors don't have real IP
        userAgent: errorData.error.userAgent,
      },
    });

    // You could add additional handling here:
    // - Send alerts to administrators
    // - Create incident tickets
    // - Trigger automatic rollbacks
    // - Update error thresholds

  } catch (error) {
    console.error('Failed to handle critical error:', error);
    // Don't throw error, as this is not critical for the main flow
  }
}

async function checkErrorPatterns(errorData: any) {
  try {
    // Check for error spikes in the last hour
    const oneHourAgo = new Date(Date.now() - 60 * 60 * 1000);
    
    const recentErrors = await prisma.analytics.count({
      where: {
        action: 'extension_error_report',
        metadata: {
          path: ['extensionId'],
          equals: errorData.extensionId,
        } as any,
        timestamp: {
          gte: oneHourAgo,
        },
      },
    });

    // Alert if error rate is high (more than 50 errors per hour)
    if (recentErrors > 50) {
      console.warn('High error rate detected for extension:', {
        extensionId: errorData.extensionId,
        errorCount: recentErrors,
        timeWindow: '1 hour',
      });

      // Create alert record
      await prisma.analytics.create({
        data: {
          userId: 'system',
          action: 'extension_error_spike_alert',
          metadata: {
            extensionId: errorData.extensionId,
            errorCount: recentErrors,
            timeWindow: '1 hour',
            triggerError: {
              type: errorData.error.type,
              category: errorData.error.category,
              severity: errorData.error.severity,
            },
          },
        },
      });
    }

    // Check for specific error patterns
    await checkSpecificPatterns(errorData);

  } catch (error) {
    console.error('Failed to check error patterns:', error);
  }
}

async function checkSpecificPatterns(errorData: any) {
  try {
    const error = errorData.error;
    
    // Check for memory leak patterns
    if (error.category === 'performance' && error.message.includes('memory')) {
      const memoryErrors = await prisma.analytics.count({
        where: {
          action: 'extension_error_report',
          metadata: {
            path: ['errorCategory'],
            equals: 'performance',
          },
          timestamp: {
            gte: new Date(Date.now() - 24 * 60 * 60 * 1000), // Last 24 hours
          },
        },
      });

      if (memoryErrors > 10) {
        console.warn('Potential memory leak pattern detected:', {
          extensionId: errorData.extensionId,
          memoryErrorCount: memoryErrors,
        });
      }
    }

    // Check for network error patterns
    if (error.category === 'network') {
      const networkErrors = await prisma.analytics.count({
        where: {
          action: 'extension_error_report',
          metadata: {
            path: ['errorCategory'],
            equals: 'network',
          } as any,
          timestamp: {
            gte: new Date(Date.now() - 30 * 60 * 1000), // Last 30 minutes
          },
        },
      });

      if (networkErrors > 20) {
        console.warn('Network connectivity issues detected:', {
          extensionId: errorData.extensionId,
          networkErrorCount: networkErrors,
        });
      }
    }

  } catch (error) {
    console.error('Failed to check specific patterns:', error);
  }
}
