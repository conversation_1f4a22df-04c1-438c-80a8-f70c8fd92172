import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { prisma } from "@/lib/database";
import { hasPermission } from "@/lib/permissions";

// GET /api/tutorials/[id]/versions - Get version history
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession();

    if (!session?.user?.email) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const user = await prisma.user.findUnique({
      where: { email: session.user.email },
    });

    if (!user) {
      return NextResponse.json({ error: "User not found" }, { status: 404 });
    }

    const tutorialId = params.id;

    // Check if tutorial exists and user has permission
    const tutorial = await prisma.tutorial.findUnique({
      where: { id: tutorialId },
      include: {
        creator: true,
        collaborators: {
          include: {
            user: true,
          },
        },
      },
    });

    if (!tutorial) {
      return NextResponse.json({ error: "Tutorial not found" }, { status: 404 });
    }

    // Check permissions
    const canRead = 
      tutorial.createdBy === user.id ||
      tutorial.collaborators.some(c => c.userId === user.id) ||
      await hasPermission(user, "TUTORIAL_READ");

    if (!canRead) {
      return NextResponse.json({ error: "Forbidden" }, { status: 403 });
    }

    // Get version history
    const versions = await prisma.tutorialVersion.findMany({
      where: { tutorialId },
      include: {
        creator: {
          select: {
            id: true,
            name: true,
            email: true,
            image: true,
          },
        },
      },
      orderBy: { createdAt: "desc" },
    });

    // Include current version
    const currentVersion = {
      id: tutorial.id,
      tutorialId: tutorial.id,
      version: tutorial.version,
      title: tutorial.title,
      description: tutorial.description,
      steps: tutorial.steps,
      metadata: tutorial.metadata,
      createdBy: tutorial.createdBy,
      createdAt: tutorial.updatedAt, // Use updatedAt for current version
      isActive: true,
      changeNotes: "Current version",
      creator: {
        id: tutorial.creator.id,
        name: tutorial.creator.name,
        email: tutorial.creator.email,
        image: tutorial.creator.image,
      },
    };

    const allVersions = [currentVersion, ...versions];

    return NextResponse.json({
      versions: allVersions,
      total: allVersions.length,
    });
  } catch (error) {
    console.error("Error fetching tutorial versions:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

// POST /api/tutorials/[id]/versions - Create new version
export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession();

    if (!session?.user?.email) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const user = await prisma.user.findUnique({
      where: { email: session.user.email },
    });

    if (!user) {
      return NextResponse.json({ error: "User not found" }, { status: 404 });
    }

    const tutorialId = params.id;
    const body = await request.json();
    const { changeNotes } = body;

    // Check if tutorial exists and user has permission
    const tutorial = await prisma.tutorial.findUnique({
      where: { id: tutorialId },
      include: {
        collaborators: true,
      },
    });

    if (!tutorial) {
      return NextResponse.json({ error: "Tutorial not found" }, { status: 404 });
    }

    // Check permissions
    const canWrite = 
      tutorial.createdBy === user.id ||
      tutorial.collaborators.some(c => c.userId === user.id && 
        JSON.parse(c.permissions as string).some((p: any) => p.action === "write" && p.granted)) ||
      await hasPermission(user, "TUTORIAL_WRITE");

    if (!canWrite) {
      return NextResponse.json({ error: "Forbidden" }, { status: 403 });
    }

    // Create version snapshot of current tutorial
    const newVersion = await prisma.tutorialVersion.create({
      data: {
        tutorialId,
        version: tutorial.version,
        title: tutorial.title,
        description: tutorial.description,
        steps: tutorial.steps,
        metadata: tutorial.metadata,
        createdBy: user.id,
        changeNotes: changeNotes || `Version ${tutorial.version} snapshot`,
      },
      include: {
        creator: {
          select: {
            id: true,
            name: true,
            email: true,
            image: true,
          },
        },
      },
    });

    // Generate new version number
    const versionParts = tutorial.version.split('.').map(Number);
    versionParts[2] += 1; // Increment patch version
    const newVersionNumber = versionParts.join('.');

    // Update tutorial with new version
    await prisma.tutorial.update({
      where: { id: tutorialId },
      data: {
        version: newVersionNumber,
        lastEditedBy: user.id,
        updatedAt: new Date(),
      },
    });

    // Log analytics
    await prisma.analytics.create({
      data: {
        userId: user.id,
        tutorialId,
        action: "tutorial_version_created",
        metadata: {
          oldVersion: tutorial.version,
          newVersion: newVersionNumber,
          changeNotes,
        },
      },
    });

    return NextResponse.json({
      message: "Version created successfully",
      version: newVersion,
      newVersionNumber,
    });
  } catch (error) {
    console.error("Error creating tutorial version:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
