import React, { useEffect, useRef } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { HighlightingConfig, HighlightingSystemProps, TutorialStep } from "./types";
import { useHighlighting } from "./useHighlighting";
import TooltipComponent from "./TooltipComponent";
import { getOptimalZIndex, getElementBounds } from "./utils";
import { cn } from "@/lib/utils";

const HighlightingSystem: React.FC<HighlightingSystemProps> = ({
  steps,
  config = {},
  onStepChange,
  onComplete,
  onSkip,
  onClose,  
  isActive = false,
  initialStep = 0,
}) => {
  const overlayRef = useRef<HTMLDivElement>(null);
  const highlightRef = useRef<HTMLDivElement>(null);

  // Memoize config to prevent infinite re-renders
  const stableConfig = React.useMemo(() => ({
    theme: "auto",
    animationDuration: 300,
    showProgress: true,
    allowSkip: true,
    keyboardNavigation: true,
    autoAdvance: false,
    autoAdvanceDelay: 5000,
    zIndexBase: 10000,
    closeOnOverlayClick: false,
    ...config,
  }), [
    config.theme,
    config.animationDuration,
    config.showProgress,
    config.allowSkip,
    config.keyboardNavigation,
    config.autoAdvance,
    config.autoAdvanceDelay,
    config.zIndexBase,
    config.closeOnOverlayClick,
  ]);

  const {
    currentStep,
    currentStepData,
    highlightedElement,
    tooltipPosition,
    navigationState,
    isActive: tutorialActive,
    nextStep,
    previousStep,
    skipTutorial,
    closeTutorial,
    startTutorial,
  } = useHighlighting({
    steps,
    config: stableConfig as HighlightingConfig,
    onStepChange,
    onComplete,
  });

  const zIndices = getOptimalZIndex(stableConfig.zIndexBase);
  const isDark =
    stableConfig.theme === "dark" ||
    (stableConfig.theme === "auto" &&
      typeof window !== "undefined" &&
      window.matchMedia &&
      window.matchMedia("(prefers-color-scheme: dark)").matches);

  // Start tutorial when isActive prop changes
  useEffect(() => {
    if (isActive && !tutorialActive) {
      startTutorial();
    } else if (!isActive && tutorialActive) {
      closeTutorial();
    }
  }, [isActive, tutorialActive, startTutorial, closeTutorial]);

  // Handle custom callbacks
  const handleNext = () => {
    nextStep();
  };

  const handlePrevious = () => {
    previousStep();
  };

  const handleSkip = () => {
    skipTutorial();
    onSkip?.();
  };

  const handleClose = () => {
    closeTutorial();
    onClose?.();
  };

  const getHighlightStyles = (): React.CSSProperties => {
    if (!highlightedElement) {
      return {};
    }

    const bounds = getElementBounds(highlightedElement);
    return {
      position: "absolute" as const,
      left: `${bounds.left - 4}px`,
      top: `${bounds.top - 4}px`,
      width: `${bounds.width + 8}px`,
      height: `${bounds.height + 8}px`,
      zIndex: zIndices.highlight,
      borderRadius: currentStepData?.highlightStyle?.borderRadius || "0",
    };
  };

  return (
    <AnimatePresence>
      {tutorialActive && (
        <>
          {/* Overlay */}
          <motion.div
            ref={overlayRef}
            className={cn(
              "fixed inset-0 pointer-events-auto",
              isDark ? "bg-black/60" : "bg-black/40",
            )}
            style={{
              zIndex: zIndices.overlay,
            }}
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{
              duration: (stableConfig.animationDuration || 300) / 1000,
              ease: "easeOut",
            }}
            onClick={(e) => {
              // Close on overlay click if allowed
              if (e.target === overlayRef.current && stableConfig.closeOnOverlayClick) {
                handleClose();
              }
            }}
          />

          {/* Highlight */}
          {highlightedElement && currentStepData && (
            <motion.div
              ref={highlightRef}
              className={cn(
                "pointer-events-none border-2 border-solid",
                currentStepData?.highlightStyle?.pulseAnimation !== false &&
                  "animate-pulse",
              )}
              style={{  
                ...getHighlightStyles(),
                borderColor: currentStepData?.highlightStyle?.borderColor || "#3b82f6",
                backgroundColor: currentStepData?.highlightStyle?.backgroundColor || "transparent",
                boxShadow: `0 0 ${currentStepData?.highlightStyle?.shadowBlur || 20}px ${currentStepData?.highlightStyle?.shadowColor || "rgba(59, 130, 246, 0.5)"}`,
                borderRadius: currentStepData?.highlightStyle?.borderRadius || "0",
              }}
              initial={{
                opacity: 0,
                scale: 0.95,
              }}
              animate={{
                opacity: 1,
                scale: 1,
              }}
              exit={{
                opacity: 0,
                scale: 0.95,
              }}
              transition={{
                duration: (stableConfig.animationDuration || 300) / 1000,
                ease: "easeOut",
              }}
            />
          )}

          {/* Tooltip */}
          <TooltipComponent
            step={currentStepData as TutorialStep}
            position={
              tooltipPosition || {
                x: 0,
                y: 0,
                placement: "top",
                arrow: { x: 0, y: 0, side: "top" },
              }
            }
            isVisible={!!tooltipPosition}
            theme={stableConfig.theme as "light" | "dark" | "auto"}
            onNext={handleNext}
            onPrevious={handlePrevious}
            onSkip={handleSkip}
            onClose={handleClose}
            navigationState={navigationState}
            animationDuration={stableConfig.animationDuration || 300}
          />
        </>
      )}
    </AnimatePresence>
  );
};

export default HighlightingSystem;
