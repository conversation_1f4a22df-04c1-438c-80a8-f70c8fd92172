"use client";

import React, { useState, useEffect } from "react";
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation";
import { useSession } from "next-auth/react";
import AdminLayout from "@/components/admin/AdminLayout";
import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { useToast } from "@/components/ui/use-toast";
import { 
  <PERSON><PERSON><PERSON>, 
  Plus, 
  Trash, 
  Save, 
  <PERSON>L<PERSON>t, 
  Eye,
  History,
  Users,
  Setting<PERSON>
} from "lucide-react";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { Tutorial, UpdateTutorialData, TutorialStep } from "@/models/Tutorial";
import { TOAST_CONFIG, ADMIN_CONFIG } from "@/lib/constants";

const tutorialEditSchema = z.object({
  title: z.string().min(3, "Title must be at least 3 characters").max(100),
  description: z.string().optional(),
  language: z.string().default("en"),
  metadata: z.object({
    category: z.string().min(1, "Category is required"),
    difficulty: z.enum(["beginner", "intermediate", "advanced"]),
    estimatedTime: z.coerce.number().min(1, "Estimated time is required"),
    tags: z.array(z.string()).default([]),
    targetUrl: z.string().url().optional().or(z.literal("")),
    version: z.string().default("1.0.0"),
  }),
  steps: z.array(
    z.object({
      id: z.string(),
      title: z.string().min(1, "Step title is required"),
      description: z.string().min(1, "Step description is required"),
      action: z.enum([
        "click",
        "hover",
        "type",
        "scroll",
        "wait",
        "explain",
        "navigate",
        "form_fill",
      ]),
      content: z.string().min(1, "Step content is required"),
      voiceContent: z.string().optional(),
      selector: z.string().optional(),
      position: z.object({
        x: z.number(),
        y: z.number(),
      }),
      duration: z.number().optional(),
      conditions: z
        .object({
          url: z.string().optional(),
          element: z.string().optional(),
          text: z.string().optional(),
        })
        .optional(),
    })
  ).min(1, "At least one step is required"),
});

type TutorialEditFormValues = z.infer<typeof tutorialEditSchema>;

export default function EditTutorialPage() {
  const { id } = useParams();
  const { data: session, status } = useSession();
  const router = useRouter();
  const { toast } = useToast();
  
  const [tutorial, setTutorial] = useState<Tutorial | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState("");
  const [activeTab, setActiveTab] = useState<"edit" | "preview" | "history" | "settings">("edit");

  const form = useForm<TutorialEditFormValues>({
    resolver: zodResolver(tutorialEditSchema),
  });

  // Load tutorial data
  useEffect(() => {
    if (id && session) {
      loadTutorial();
    }
  }, [id, session]);

  const loadTutorial = async () => {
    try {
      setIsLoading(true);
      const response = await fetch(`/api/tutorials/${id}`);
      
      if (!response.ok) {
        throw new Error("Failed to load tutorial");
      }

      const data = await response.json();
      const tutorialData = data.tutorial;
      
      setTutorial(tutorialData);
      
      // Populate form with tutorial data
      form.reset({
        title: tutorialData.title,
        description: tutorialData.description || "",
        language: tutorialData.language,
        metadata: {
          category: tutorialData.metadata.category,
          difficulty: tutorialData.metadata.difficulty,
          estimatedTime: tutorialData.metadata.estimatedTime,
          tags: tutorialData.metadata.tags || [],
          targetUrl: tutorialData.metadata.targetUrl || "",
          version: tutorialData.metadata.version || "1.0.0",
        },
        steps: tutorialData.steps.map((step: any, index: number) => ({
          ...step,
          id: step.id || `step-${index + 1}`,
        })),
      });
    } catch (error) {
      console.error("Error loading tutorial:", error);
      setError("Failed to load tutorial");
      toast({
        title: "Error",
        description: "Failed to load tutorial",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const onSubmit = async (data: TutorialEditFormValues) => {
    if (!tutorial) return;

    try {
      setIsSubmitting(true);
      setError("");

      const updateData: UpdateTutorialData = {
        title: data.title,
        description: data.description,
        language: data.language,
        steps: data.steps,
        metadata: {
          ...tutorial.metadata,
          ...data.metadata,
        },
      };

      const response = await fetch(`/api/tutorials/${id}`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(updateData),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to update tutorial");
      }

      toast({
        title: "Success",
        description: "Tutorial updated successfully",
        variant: "default",
      });

      // Reload tutorial data
      await loadTutorial();
    } catch (error) {
      console.error("Error updating tutorial:", error);
      setError(error instanceof Error ? error.message : "Failed to update tutorial");
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to update tutorial",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const addStep = () => {
    const currentSteps = form.getValues("steps");
    const newStep = {
      id: `step-${Date.now()}`,
      title: "",
      description: "",
      action: "explain" as const,
      content: "",
      position: { x: 0, y: 0 },
    };
    form.setValue("steps", [...currentSteps, newStep]);
  };

  const removeStep = (index: number) => {
    const currentSteps = form.getValues("steps");
    if (currentSteps.length > 1) {
      const newSteps = currentSteps.filter((_, i) => i !== index);
      form.setValue("steps", newSteps);
    }
  };

  if (status === "loading" || isLoading) {
    return (
      <AdminLayout title="Loading..." description="Loading tutorial...">
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
        </div>
      </AdminLayout>
    );
  }

  if (!session) {
    router.push("/auth/signin");
    return null;
  }

  if (error && !tutorial) {
    return (
      <AdminLayout title="Error" description="Failed to load tutorial">
        <Alert variant="destructive">
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout
      title={`Edit Tutorial: ${tutorial?.title || "Loading..."}`}
      description="Edit and manage tutorial content and settings."
    >
      <div className="space-y-6">
        {/* Header with navigation */}
        <div className="flex items-center justify-between">
          <Button
            variant="outline"
            size="sm"
            className="gap-2"
            onClick={() => router.push("/admin/tutorials")}
          >
            <ArrowLeft className="h-4 w-4" />
            Back to Tutorials
          </Button>

          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => window.open(`/tutorials/${id}`, '_blank')}
              className="gap-2"
            >
              <Eye className="h-4 w-4" />
              Preview
            </Button>
            
            <Button
              type="submit"
              form="tutorial-edit-form"
              disabled={isSubmitting}
              className="gap-2"
            >
              <Save className="h-4 w-4" />
              {isSubmitting ? "Saving..." : "Save Changes"}
            </Button>
          </div>
        </div>

        {error && (
          <Alert variant="destructive">
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {tutorial && (
          <Form {...form}>
            <form id="tutorial-edit-form" onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
              {/* Basic Information */}
              <Card>
                <CardHeader>
                  <CardTitle>Basic Information</CardTitle>
                  <CardDescription>
                    Update the basic details of your tutorial
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <FormField
                      control={form.control}
                      name="title"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Title</FormLabel>
                          <FormControl>
                            <Input placeholder="Enter tutorial title" {...field} />
                          </FormControl>
                          <FormDescription>
                            A clear and concise title for the tutorial
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="language"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Language</FormLabel>
                          <Select onValueChange={field.onChange} defaultValue={field.value}>
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue placeholder="Select language" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              <SelectItem value="en">English</SelectItem>
                              <SelectItem value="es">Spanish</SelectItem>
                              <SelectItem value="fr">French</SelectItem>
                              <SelectItem value="de">German</SelectItem>
                              <SelectItem value="it">Italian</SelectItem>
                              <SelectItem value="pt">Portuguese</SelectItem>
                            </SelectContent>
                          </Select>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  <FormField
                    control={form.control}
                    name="description"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Description</FormLabel>
                        <FormControl>
                          <Textarea
                            placeholder="Describe what this tutorial teaches..."
                            className="min-h-[100px]"
                            {...field}
                          />
                        </FormControl>
                        <FormDescription>
                          A detailed description of what users will learn
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </CardContent>
              </Card>

              {/* Metadata */}
              <Card>
                <CardHeader>
                  <CardTitle>Tutorial Metadata</CardTitle>
                  <CardDescription>
                    Configure tutorial settings and categorization
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <FormField
                      control={form.control}
                      name="metadata.category"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Category</FormLabel>
                          <Select onValueChange={field.onChange} defaultValue={field.value}>
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue placeholder="Select category" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              <SelectItem value="web-navigation">Web Navigation</SelectItem>
                              <SelectItem value="form-filling">Form Filling</SelectItem>
                              <SelectItem value="e-commerce">E-commerce</SelectItem>
                              <SelectItem value="productivity">Productivity</SelectItem>
                              <SelectItem value="social-media">Social Media</SelectItem>
                              <SelectItem value="education">Education</SelectItem>
                              <SelectItem value="entertainment">Entertainment</SelectItem>
                              <SelectItem value="other">Other</SelectItem>
                            </SelectContent>
                          </Select>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="metadata.difficulty"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Difficulty</FormLabel>
                          <Select onValueChange={field.onChange} defaultValue={field.value}>
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue placeholder="Select difficulty" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              <SelectItem value="beginner">Beginner</SelectItem>
                              <SelectItem value="intermediate">Intermediate</SelectItem>
                              <SelectItem value="advanced">Advanced</SelectItem>
                            </SelectContent>
                          </Select>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="metadata.estimatedTime"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Estimated Time (minutes)</FormLabel>
                          <FormControl>
                            <Input
                              type="number"
                              min="1"
                              placeholder="5"
                              {...field}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  <FormField
                    control={form.control}
                    name="metadata.targetUrl"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Target URL</FormLabel>
                        <FormControl>
                          <Input
                            placeholder="https://example.com"
                            {...field}
                          />
                        </FormControl>
                        <FormDescription>
                          The website where this tutorial should be used
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </CardContent>
              </Card>

              {/* Tutorial Steps */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center justify-between">
                    <span>Tutorial Steps</span>
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={addStep}
                      className="gap-1"
                    >
                      <Plus className="h-4 w-4" /> Add Step
                    </Button>
                  </CardTitle>
                  <CardDescription>
                    Define the steps users will follow in this tutorial
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  {form.watch("steps")?.map((step, index) => (
                    <Card key={step.id || index} className="relative">
                      <CardHeader className="pb-3">
                        <div className="flex items-center justify-between">
                          <h3 className="font-medium">Step {index + 1}</h3>
                          {form.watch("steps").length > 1 && (
                            <Button
                              type="button"
                              variant="ghost"
                              size="sm"
                              onClick={() => removeStep(index)}
                              className="text-destructive hover:text-destructive"
                            >
                              <Trash className="h-4 w-4" />
                            </Button>
                          )}
                        </div>
                      </CardHeader>
                      <CardContent className="space-y-4">
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <FormField
                            control={form.control}
                            name={`steps.${index}.title`}
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>Step Title</FormLabel>
                                <FormControl>
                                  <Input placeholder="Enter step title" {...field} />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />

                          <FormField
                            control={form.control}
                            name={`steps.${index}.action`}
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>Action Type</FormLabel>
                                <Select onValueChange={field.onChange} defaultValue={field.value}>
                                  <FormControl>
                                    <SelectTrigger>
                                      <SelectValue placeholder="Select action" />
                                    </SelectTrigger>
                                  </FormControl>
                                  <SelectContent>
                                    <SelectItem value="explain">Explain</SelectItem>
                                    <SelectItem value="click">Click</SelectItem>
                                    <SelectItem value="hover">Hover</SelectItem>
                                    <SelectItem value="type">Type</SelectItem>
                                    <SelectItem value="scroll">Scroll</SelectItem>
                                    <SelectItem value="wait">Wait</SelectItem>
                                    <SelectItem value="navigate">Navigate</SelectItem>
                                    <SelectItem value="form_fill">Form Fill</SelectItem>
                                  </SelectContent>
                                </Select>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                        </div>

                        <FormField
                          control={form.control}
                          name={`steps.${index}.description`}
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Description</FormLabel>
                              <FormControl>
                                <Textarea
                                  placeholder="Describe what the user should do in this step"
                                  className="min-h-[80px]"
                                  {...field}
                                />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />

                        <FormField
                          control={form.control}
                          name={`steps.${index}.content`}
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Content</FormLabel>
                              <FormControl>
                                <Textarea
                                  placeholder="The main content that will be shown to the user"
                                  className="min-h-[100px]"
                                  {...field}
                                />
                              </FormControl>
                              <FormDescription>
                                This is the main instructional content for this step
                              </FormDescription>
                              <FormMessage />
                            </FormItem>
                          )}
                        />

                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <FormField
                            control={form.control}
                            name={`steps.${index}.selector`}
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>CSS Selector (Optional)</FormLabel>
                                <FormControl>
                                  <Input
                                    placeholder="e.g., #button-id, .class-name"
                                    {...field}
                                  />
                                </FormControl>
                                <FormDescription>
                                  CSS selector for the target element
                                </FormDescription>
                                <FormMessage />
                              </FormItem>
                            )}
                          />

                          <FormField
                            control={form.control}
                            name={`steps.${index}.duration`}
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>Duration (seconds)</FormLabel>
                                <FormControl>
                                  <Input
                                    type="number"
                                    min="0"
                                    placeholder="5"
                                    {...field}
                                  />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                        </div>

                        <FormField
                          control={form.control}
                          name={`steps.${index}.voiceContent`}
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Voice Content (Optional)</FormLabel>
                              <FormControl>
                                <Textarea
                                  placeholder="Text to be spoken for this step"
                                  className="min-h-[60px]"
                                  {...field}
                                />
                              </FormControl>
                              <FormDescription>
                                Optional voice narration for this step
                              </FormDescription>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </CardContent>
                    </Card>
                  ))}

                  {(!form.watch("steps") || form.watch("steps").length === 0) && (
                    <div className="text-center py-8 text-muted-foreground">
                      <BookOpen className="h-12 w-12 mx-auto mb-4 opacity-50" />
                      <p>No steps added yet. Click "Add Step" to get started.</p>
                    </div>
                  )}
                </CardContent>
              </Card>
            </form>
          </Form>
        )}
      </div>
    </AdminLayout>
  );
}
