import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { prisma } from "@/lib/database";
import { hasPermission } from "@/lib/permissions";
import { z } from "zod";

const addCollaboratorSchema = z.object({
  email: z.string().email(),
  role: z.enum(["owner", "editor", "reviewer", "viewer"]).default("viewer"),
  permissions: z.array(z.object({
    action: z.enum(["read", "write", "delete", "publish", "share", "manage_collaborators"]),
    granted: z.boolean(),
  })).default([
    { action: "read", granted: true },
  ]),
  message: z.string().optional(),
});

const updateCollaboratorSchema = z.object({
  role: z.enum(["owner", "editor", "reviewer", "viewer"]).optional(),
  permissions: z.array(z.object({
    action: z.enum(["read", "write", "delete", "publish", "share", "manage_collaborators"]),
    granted: z.boolean(),
  })).optional(),
});

// GET /api/tutorials/[id]/collaborators - Get tutorial collaborators
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession();

    if (!session?.user?.email) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const user = await prisma.user.findUnique({
      where: { email: session.user.email },
    });

    if (!user) {
      return NextResponse.json({ error: "User not found" }, { status: 404 });
    }

    const tutorialId = params.id;

    // Check if tutorial exists and user has permission to view collaborators
    const tutorial = await prisma.tutorial.findUnique({
      where: { id: tutorialId },
      include: {
        creator: {
          select: {
            id: true,
            name: true,
            email: true,
            image: true,
          },
        },
        collaborators: {
          include: {
            user: {
              select: {
                id: true,
                name: true,
                email: true,
                image: true,
              },
            },
            addedByUser: {
              select: {
                id: true,
                name: true,
                email: true,
              },
            },
          },
        },
      },
    });

    if (!tutorial) {
      return NextResponse.json({ error: "Tutorial not found" }, { status: 404 });
    }

    // Check permissions
    const canView = 
      tutorial.createdBy === user.id ||
      tutorial.collaborators.some(c => c.userId === user.id) ||
      await hasPermission(user, "TUTORIAL_READ");

    if (!canView) {
      return NextResponse.json({ error: "Forbidden" }, { status: 403 });
    }

    // Format collaborators data
    const collaborators = [
      // Owner
      {
        id: `owner-${tutorial.creator.id}`,
        user: tutorial.creator,
        role: "owner",
        permissions: [
          { action: "read", granted: true },
          { action: "write", granted: true },
          { action: "delete", granted: true },
          { action: "publish", granted: true },
          { action: "share", granted: true },
          { action: "manage_collaborators", granted: true },
        ],
        addedAt: tutorial.createdAt,
        addedBy: null,
        isOwner: true,
      },
      // Collaborators
      ...tutorial.collaborators.map(c => ({
        id: c.id,
        user: c.user,
        role: c.role,
        permissions: JSON.parse(c.permissions as string),
        addedAt: c.addedAt,
        addedBy: c.addedByUser,
        isOwner: false,
      })),
    ];

    return NextResponse.json({
      collaborators,
      total: collaborators.length,
    });
  } catch (error) {
    console.error("Error fetching collaborators:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

// POST /api/tutorials/[id]/collaborators - Add collaborator
export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession();

    if (!session?.user?.email) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const user = await prisma.user.findUnique({
      where: { email: session.user.email },
    });

    if (!user) {
      return NextResponse.json({ error: "User not found" }, { status: 404 });
    }

    const tutorialId = params.id;

    // Check if tutorial exists and user has permission to manage collaborators
    const tutorial = await prisma.tutorial.findUnique({
      where: { id: tutorialId },
      include: {
        collaborators: true,
      },
    });

    if (!tutorial) {
      return NextResponse.json({ error: "Tutorial not found" }, { status: 404 });
    }

    // Check permissions
    const canManageCollaborators = 
      tutorial.createdBy === user.id ||
      tutorial.collaborators.some(c => 
        c.userId === user.id && 
        JSON.parse(c.permissions as string).some((p: any) => p.action === "manage_collaborators" && p.granted)
      ) ||
      await hasPermission(user, "TUTORIAL_WRITE");

    if (!canManageCollaborators) {
      return NextResponse.json({ error: "Forbidden" }, { status: 403 });
    }

    const body = await request.json();
    const validatedData = addCollaboratorSchema.parse(body);

    // Find target user
    const targetUser = await prisma.user.findUnique({
      where: { email: validatedData.email },
    });

    if (!targetUser) {
      return NextResponse.json({ error: "User not found" }, { status: 404 });
    }

    // Check if user is already a collaborator
    const existingCollaborator = await prisma.tutorialCollaborator.findUnique({
      where: {
        tutorialId_userId: {
          tutorialId,
          userId: targetUser.id,
        },
      },
    });

    if (existingCollaborator) {
      return NextResponse.json({ error: "User is already a collaborator" }, { status: 400 });
    }

    // Check if trying to add the owner
    if (targetUser.id === tutorial.createdBy) {
      return NextResponse.json({ error: "Cannot add owner as collaborator" }, { status: 400 });
    }

    // Create collaborator
    const collaborator = await prisma.tutorialCollaborator.create({
      data: {
        tutorialId,
        userId: targetUser.id,
        role: validatedData.role,
        permissions: JSON.stringify(validatedData.permissions),
        addedBy: user.id,
      },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
            image: true,
          },
        },
        addedByUser: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
    });

    // Log activity
    await prisma.analytics.create({
      data: {
        userId: user.id,
        tutorialId,
        action: "collaborator_added",
        metadata: {
          collaboratorId: collaborator.id,
          collaboratorEmail: validatedData.email,
          role: validatedData.role,
          permissions: validatedData.permissions,
        },
      },
    });

    // Notify the new collaborator
    await prisma.analytics.create({
      data: {
        userId: targetUser.id,
        tutorialId,
        action: "collaboration_invitation",
        metadata: {
          invitedBy: user.id,
          role: validatedData.role,
          message: validatedData.message,
        },
      },
    });

    return NextResponse.json({
      message: "Collaborator added successfully",
      collaborator: {
        id: collaborator.id,
        user: collaborator.user,
        role: collaborator.role,
        permissions: JSON.parse(collaborator.permissions as string),
        addedAt: collaborator.addedAt,
        addedBy: collaborator.addedByUser,
        isOwner: false,
      },
    });
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Invalid request data", details: error.errors },
        { status: 400 }
      );
    }

    console.error("Error adding collaborator:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

// PUT /api/tutorials/[id]/collaborators/[collaboratorId] - Update collaborator
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession();

    if (!session?.user?.email) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const user = await prisma.user.findUnique({
      where: { email: session.user.email },
    });

    if (!user) {
      return NextResponse.json({ error: "User not found" }, { status: 404 });
    }

    const tutorialId = params.id;
    const { searchParams } = new URL(request.url);
    const collaboratorId = searchParams.get("collaboratorId");

    if (!collaboratorId) {
      return NextResponse.json({ error: "Collaborator ID is required" }, { status: 400 });
    }

    // Check permissions (similar to POST)
    const tutorial = await prisma.tutorial.findUnique({
      where: { id: tutorialId },
      include: { collaborators: true },
    });

    if (!tutorial) {
      return NextResponse.json({ error: "Tutorial not found" }, { status: 404 });
    }

    const canManageCollaborators = 
      tutorial.createdBy === user.id ||
      tutorial.collaborators.some(c => 
        c.userId === user.id && 
        JSON.parse(c.permissions as string).some((p: any) => p.action === "manage_collaborators" && p.granted)
      );

    if (!canManageCollaborators) {
      return NextResponse.json({ error: "Forbidden" }, { status: 403 });
    }

    const body = await request.json();
    const validatedData = updateCollaboratorSchema.parse(body);

    // Update collaborator
    const updatedCollaborator = await prisma.tutorialCollaborator.update({
      where: { id: collaboratorId },
      data: {
        ...(validatedData.role && { role: validatedData.role }),
        ...(validatedData.permissions && { permissions: JSON.stringify(validatedData.permissions) }),
      },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
            image: true,
          },
        },
      },
    });

    // Log activity
    await prisma.analytics.create({
      data: {
        userId: user.id,
        tutorialId,
        action: "collaborator_updated",
        metadata: {
          collaboratorId,
          changes: validatedData,
        },
      },
    });

    return NextResponse.json({
      message: "Collaborator updated successfully",
      collaborator: {
        id: updatedCollaborator.id,
        user: updatedCollaborator.user,
        role: updatedCollaborator.role,
        permissions: JSON.parse(updatedCollaborator.permissions as string),
        addedAt: updatedCollaborator.addedAt,
        isOwner: false,
      },
    });
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Invalid request data", details: error.errors },
        { status: 400 }
      );
    }

    console.error("Error updating collaborator:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

// DELETE /api/tutorials/[id]/collaborators/[collaboratorId] - Remove collaborator
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession();

    if (!session?.user?.email) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const user = await prisma.user.findUnique({
      where: { email: session.user.email },
    });

    if (!user) {
      return NextResponse.json({ error: "User not found" }, { status: 404 });
    }

    const tutorialId = params.id;
    const { searchParams } = new URL(request.url);
    const collaboratorId = searchParams.get("collaboratorId");

    if (!collaboratorId) {
      return NextResponse.json({ error: "Collaborator ID is required" }, { status: 400 });
    }

    // Get collaborator info before deletion
    const collaborator = await prisma.tutorialCollaborator.findUnique({
      where: { id: collaboratorId },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
    });

    if (!collaborator) {
      return NextResponse.json({ error: "Collaborator not found" }, { status: 404 });
    }

    // Check permissions (owner or self-removal)
    const tutorial = await prisma.tutorial.findUnique({
      where: { id: tutorialId },
    });

    const canRemove = 
      tutorial?.createdBy === user.id || // Owner can remove anyone
      collaborator.userId === user.id; // User can remove themselves

    if (!canRemove) {
      return NextResponse.json({ error: "Forbidden" }, { status: 403 });
    }

    // Remove collaborator
    await prisma.tutorialCollaborator.delete({
      where: { id: collaboratorId },
    });

    // Log activity
    await prisma.analytics.create({
      data: {
        userId: user.id,
        tutorialId,
        action: "collaborator_removed",
        metadata: {
          collaboratorId,
          collaboratorEmail: collaborator.user.email,
          removedBy: user.id,
        },
      },
    });

    return NextResponse.json({
      message: "Collaborator removed successfully",
    });
  } catch (error) {
    console.error("Error removing collaborator:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
