import { NextRequest, NextResponse } from "next/server";
import { CertificateService } from "@/lib/certificate-service";

// GET /api/certificates/verify/[code] - Verify a certificate
export async function GET(
  request: NextRequest,
  { params }: { params: { code: string } }
) {
  try {
    const verificationCode = params.code;

    if (!verificationCode) {
      return NextResponse.json(
        { error: "Verification code is required" },
        { status: 400 }
      );
    }

    const certificate = await CertificateService.verifyCertificate(verificationCode);

    return NextResponse.json({
      valid: true,
      certificate: {
        id: certificate.id,
        certificateNumber: certificate.certificateNumber,
        title: certificate.title,
        description: certificate.description,
        recipientName: certificate.recipientName,
        recipientEmail: certificate.recipientEmail,
        issuerName: certificate.issuerName,
        issuerTitle: certificate.issuerTitle,
        issuedAt: certificate.issuedAt,
        completionDate: certificate.completionDate,
        completionTime: certificate.completionTime,
        finalScore: certificate.finalScore,
        achievements: certificate.achievements,
        tutorial: {
          id: certificate.tutorial.id,
          title: certificate.tutorial.title,
          description: certificate.tutorial.description,
          metadata: certificate.tutorial.metadata,
        },
        template: certificate.template ? {
          id: certificate.template.id,
          name: certificate.template.name,
          layout: certificate.template.layout,
          colorScheme: certificate.template.colorScheme,
        } : null,
        metadata: certificate.metadata,
      },
    });
  } catch (error) {
    console.error("Error verifying certificate:", error);
    
    if (error instanceof Error) {
      if (error.message.includes("not found")) {
        return NextResponse.json(
          { valid: false, error: "Certificate not found" },
          { status: 404 }
        );
      }
      if (error.message.includes("revoked")) {
        return NextResponse.json(
          { valid: false, error: "Certificate has been revoked" },
          { status: 410 }
        );
      }
      if (error.message.includes("expired")) {
        return NextResponse.json(
          { valid: false, error: "Certificate has expired" },
          { status: 410 }
        );
      }
    }

    return NextResponse.json(
      { valid: false, error: "Internal server error" },
      { status: 500 }
    );
  }
}
