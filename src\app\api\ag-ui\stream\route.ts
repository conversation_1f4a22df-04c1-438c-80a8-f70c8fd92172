/**
 * AG-UI Server-Sent Events Stream Endpoint
 * Replaces mock AI responses with real AG-UI streaming
 */

import { NextRequest, NextResponse } from 'next/server';
import { getAgUIServer } from '@/lib/ag-ui-server';
import type { AgentEvent } from '@/lib/ag-ui-types';

export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url);
  const apiKey = searchParams.get('apiKey');
  
  // Validate API key if required
  if (process.env.AG_UI_API_KEY && apiKey !== process.env.AG_UI_API_KEY) {
    return new NextResponse('Unauthorized', { status: 401 });
  }

  // Create Server-Sent Events stream
  const encoder = new TextEncoder();
  const stream = new ReadableStream({
    start(controller) {
      const agUIServer = getAgUIServer();
      
      // Set up event listener
      const eventListener = (event: AgentEvent) => {
        const data = `data: ${JSON.stringify(event)}\n\n`;
        controller.enqueue(encoder.encode(data));
      };

      agUIServer.onEvent(eventListener);

      // Send initial connection event
      const connectionEvent: AgentEvent = {
        type: 'Custom',
        rawEvent: {
          name: 'connection_established',
          value: { timestamp: new Date().toISOString() }
        }
      };
      
      const initialData = `data: ${JSON.stringify(connectionEvent)}\n\n`;
      controller.enqueue(encoder.encode(initialData));

      // Handle client disconnect
      request.signal.addEventListener('abort', () => {
        controller.close();
      });
    }
  });

  return new NextResponse(stream, {
    headers: {
      'Content-Type': 'text/event-stream',
      'Cache-Control': 'no-cache',
      'Connection': 'keep-alive',
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization'
    }
  });
}

export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization'
    }
  });
}
