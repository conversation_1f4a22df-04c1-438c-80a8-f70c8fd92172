import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { prisma } from "@/lib/database";
import { hasPermission } from "@/lib/permissions";
import { z } from "zod";
import crypto from "crypto";

const shareRequestSchema = z.object({
  shareType: z.enum(["public", "private", "team", "link"]),
  permissions: z.array(z.enum(["read", "write", "comment", "share"])).default(["read"]),
  expiresAt: z.string().datetime().optional(),
  allowComments: z.boolean().default(true),
  allowDownload: z.boolean().default(false),
  requireAuth: z.boolean().default(true),
  teamIds: z.array(z.string()).optional(),
  userEmails: z.array(z.string().email()).optional(),
  message: z.string().optional(),
});

// POST /api/tutorials/[id]/share - Create sharing link or share with users/teams
export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession();

    if (!session?.user?.email) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const user = await prisma.user.findUnique({
      where: { email: session.user.email },
    });

    if (!user) {
      return NextResponse.json({ error: "User not found" }, { status: 404 });
    }

    const tutorialId = params.id;

    // Check if tutorial exists and user has permission to share
    const tutorial = await prisma.tutorial.findUnique({
      where: { id: tutorialId },
      include: {
        collaborators: true,
      },
    });

    if (!tutorial) {
      return NextResponse.json({ error: "Tutorial not found" }, { status: 404 });
    }

    // Check permissions
    const canShare = 
      tutorial.createdBy === user.id ||
      tutorial.collaborators.some(c => 
        c.userId === user.id && 
        JSON.parse(c.permissions as string).some((p: any) => p.action === "share" && p.granted)
      ) ||
      await hasPermission(user, "TUTORIAL_PUBLISH");

    if (!canShare) {
      return NextResponse.json({ error: "Forbidden" }, { status: 403 });
    }

    const body = await request.json();
    const validatedData = shareRequestSchema.parse(body);

    let shareResult: any = {};

    switch (validatedData.shareType) {
      case "public":
        shareResult = await createPublicShare(tutorialId, user.id, validatedData);
        break;
      case "private":
        shareResult = await createPrivateShare(tutorialId, user.id, validatedData);
        break;
      case "team":
        shareResult = await createTeamShare(tutorialId, user.id, validatedData);
        break;
      case "link":
        shareResult = await createLinkShare(tutorialId, user.id, validatedData);
        break;
    }

    // Log sharing activity
    await prisma.analytics.create({
      data: {
        userId: user.id,
        tutorialId,
        action: "tutorial_shared",
        metadata: {
          shareType: validatedData.shareType,
          permissions: validatedData.permissions,
          recipientCount: (validatedData.userEmails?.length || 0) + (validatedData.teamIds?.length || 0),
          expiresAt: validatedData.expiresAt,
        },
      },
    });

    return NextResponse.json({
      message: "Tutorial shared successfully",
      share: shareResult,
    });
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Invalid request data", details: error.errors },
        { status: 400 }
      );
    }

    console.error("Error sharing tutorial:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

// GET /api/tutorials/[id]/share - Get sharing information
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession();

    if (!session?.user?.email) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const user = await prisma.user.findUnique({
      where: { email: session.user.email },
    });

    if (!user) {
      return NextResponse.json({ error: "User not found" }, { status: 404 });
    }

    const tutorialId = params.id;

    // Check if tutorial exists and user has permission to view sharing info
    const tutorial = await prisma.tutorial.findUnique({
      where: { id: tutorialId },
      include: {
        collaborators: {
          include: {
            user: {
              select: {
                id: true,
                name: true,
                email: true,
                image: true,
              },
            },
          },
        },
      },
    });

    if (!tutorial) {
      return NextResponse.json({ error: "Tutorial not found" }, { status: 404 });
    }

    // Check permissions
    const canViewSharing = 
      tutorial.createdBy === user.id ||
      tutorial.collaborators.some(c => c.userId === user.id) ||
      await hasPermission(user, "TUTORIAL_READ");

    if (!canViewSharing) {
      return NextResponse.json({ error: "Forbidden" }, { status: 403 });
    }

    // Get sharing links (stored in tutorial metadata or separate table)
    const sharingInfo = {
      isPublic: tutorial.status === "published",
      collaborators: tutorial.collaborators.map(c => ({
        id: c.id,
        user: c.user,
        role: c.role,
        permissions: JSON.parse(c.permissions as string),
        addedAt: c.addedAt,
      })),
      // Additional sharing info would be retrieved from sharing table if implemented
    };

    return NextResponse.json({ sharing: sharingInfo });
  } catch (error) {
    console.error("Error fetching sharing info:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

async function createPublicShare(tutorialId: string, userId: string, data: any) {
  // Update tutorial to be publicly accessible
  await prisma.tutorial.update({
    where: { id: tutorialId },
    data: {
      status: "published",
      publishedAt: new Date(),
    },
  });

  return {
    type: "public",
    url: `${process.env.NEXTAUTH_URL}/tutorials/${tutorialId}`,
    isPublic: true,
  };
}

async function createPrivateShare(tutorialId: string, userId: string, data: any) {
  const results = [];

  if (data.userEmails) {
    for (const email of data.userEmails) {
      // Find user by email
      const targetUser = await prisma.user.findUnique({
        where: { email },
      });

      if (targetUser) {
        // Add as collaborator
        const collaborator = await prisma.tutorialCollaborator.upsert({
          where: {
            tutorialId_userId: {
              tutorialId,
              userId: targetUser.id,
            },
          },
          update: {
            permissions: JSON.stringify(
              data.permissions.map((p: string) => ({ action: p, granted: true }))
            ),
          },
          create: {
            tutorialId,
            userId: targetUser.id,
            role: "viewer",
            permissions: JSON.stringify(
              data.permissions.map((p: string) => ({ action: p, granted: true }))
            ),
            addedBy: userId,
          },
        });

        results.push({
          email,
          userId: targetUser.id,
          status: "added",
          collaboratorId: collaborator.id,
        });

        // Send notification (would implement email service)
        await prisma.analytics.create({
          data: {
            userId: targetUser.id,
            tutorialId,
            action: "tutorial_shared_with_user",
            metadata: {
              sharedBy: userId,
              permissions: data.permissions,
            },
          },
        });
      } else {
        results.push({
          email,
          status: "user_not_found",
        });
      }
    }
  }

  return {
    type: "private",
    results,
  };
}

async function createTeamShare(tutorialId: string, userId: string, data: any) {
  // Team sharing would require a teams table - placeholder implementation
  return {
    type: "team",
    message: "Team sharing not yet implemented",
  };
}

async function createLinkShare(tutorialId: string, userId: string, data: any) {
  // Generate secure sharing token
  const shareToken = crypto.randomBytes(32).toString("hex");
  
  // Store sharing link info (would need a separate table)
  const shareLink = {
    token: shareToken,
    tutorialId,
    createdBy: userId,
    permissions: data.permissions,
    expiresAt: data.expiresAt ? new Date(data.expiresAt) : null,
    allowComments: data.allowComments,
    allowDownload: data.allowDownload,
    requireAuth: data.requireAuth,
  };

  // In a real implementation, this would be stored in a database table
  // For now, we'll store it in the tutorial metadata
  const tutorial = await prisma.tutorial.findUnique({
    where: { id: tutorialId },
  });

  const metadata = tutorial?.metadata as any || {};
  metadata.shareLinks = metadata.shareLinks || [];
  metadata.shareLinks.push(shareLink);

  await prisma.tutorial.update({
    where: { id: tutorialId },
    data: { metadata },
  });

  return {
    type: "link",
    url: `${process.env.NEXTAUTH_URL}/shared/${shareToken}`,
    token: shareToken,
    expiresAt: shareLink.expiresAt,
    permissions: data.permissions,
  };
}
