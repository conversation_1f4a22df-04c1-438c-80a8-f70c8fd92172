"use client";

import React, { useState, useEffect } from "react";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import {
  Ta<PERSON>,
  <PERSON><PERSON><PERSON>onte<PERSON>,
  <PERSON><PERSON><PERSON>ist,
  TabsTrigger,
} from "@/components/ui/tabs";
import {
  Trophy,
  Star,
  Zap,
  Target,
  Award,
  Clock,
  TrendingUp,
  Medal,
  Crown,
  Gem,
} from "lucide-react";
import { formatDistanceToNow } from "date-fns";

interface Achievement {
  id: string;
  name: string;
  description: string;
  category: string;
  type: string;
  iconUrl?: string;
  badgeColor: string;
  points: number;
  rarity: string;
  isEarned?: boolean;
  earnedAt?: string;
  progress?: number;
}

interface UserAchievement {
  id: string;
  earnedAt: string;
  progress: number;
  metadata: any;
  achievement: Achievement;
  tutorial?: {
    id: string;
    title: string;
    metadata: any;
  };
}

interface AchievementStats {
  totalEarned: number;
  totalAvailable: number;
  totalPoints: number;
  byCategory: Record<string, number>;
  byRarity: Record<string, number>;
}

interface AchievementDisplayProps {
  userId?: string;
  showProgress?: boolean;
  compact?: boolean;
}

export default function AchievementDisplay({
  userId,
  showProgress = true,
  compact = false,
}: AchievementDisplayProps) {
  const [userAchievements, setUserAchievements] = useState<UserAchievement[]>([]);
  const [availableAchievements, setAvailableAchievements] = useState<Achievement[]>([]);
  const [stats, setStats] = useState<AchievementStats | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [activeTab, setActiveTab] = useState("earned");

  useEffect(() => {
    loadAchievements();
  }, [userId]);

  const loadAchievements = async () => {
    try {
      setIsLoading(true);
      const params = new URLSearchParams({
        ...(showProgress && { includeProgress: "true" }),
      });

      const response = await fetch(`/api/achievements?${params}`);
      
      if (response.ok) {
        const data = await response.json();
        setUserAchievements(data.achievements);
        setStats(data.stats);
        if (data.availableAchievements) {
          setAvailableAchievements(data.availableAchievements);
        }
      }
    } catch (error) {
      console.error("Error loading achievements:", error);
    } finally {
      setIsLoading(false);
    }
  };

  const getRarityIcon = (rarity: string) => {
    switch (rarity) {
      case "legendary":
        return <Crown className="h-4 w-4 text-yellow-500" />;
      case "epic":
        return <Gem className="h-4 w-4 text-purple-500" />;
      case "rare":
        return <Medal className="h-4 w-4 text-blue-500" />;
      case "uncommon":
        return <Star className="h-4 w-4 text-green-500" />;
      default:
        return <Trophy className="h-4 w-4 text-gray-500" />;
    }
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case "completion":
        return <Award className="h-4 w-4" />;
      case "speed":
        return <Zap className="h-4 w-4" />;
      case "accuracy":
        return <Target className="h-4 w-4" />;
      case "streak":
        return <TrendingUp className="h-4 w-4" />;
      case "milestone":
        return <Trophy className="h-4 w-4" />;
      default:
        return <Medal className="h-4 w-4" />;
    }
  };

  const getRarityColor = (rarity: string) => {
    switch (rarity) {
      case "legendary":
        return "border-yellow-400 bg-yellow-50 text-yellow-800";
      case "epic":
        return "border-purple-400 bg-purple-50 text-purple-800";
      case "rare":
        return "border-blue-400 bg-blue-50 text-blue-800";
      case "uncommon":
        return "border-green-400 bg-green-50 text-green-800";
      default:
        return "border-gray-400 bg-gray-50 text-gray-800";
    }
  };

  const AchievementCard = ({ achievement, userAchievement }: { 
    achievement: Achievement; 
    userAchievement?: UserAchievement;
  }) => {
    const isEarned = !!userAchievement || achievement.isEarned;
    const progress = userAchievement?.progress || achievement.progress || 0;

    return (
      <Card className={`transition-all ${isEarned ? 'ring-2 ring-primary/20' : 'opacity-75'}`}>
        <CardContent className="p-4">
          <div className="flex items-start gap-3">
            <div className={`p-2 rounded-full ${isEarned ? 'bg-primary/10' : 'bg-muted'}`}>
              {getTypeIcon(achievement.type)}
            </div>
            
            <div className="flex-1 min-w-0">
              <div className="flex items-center gap-2 mb-1">
                <h3 className={`font-medium truncate ${isEarned ? 'text-foreground' : 'text-muted-foreground'}`}>
                  {achievement.name}
                </h3>
                {getRarityIcon(achievement.rarity)}
              </div>
              
              <p className={`text-sm mb-2 ${isEarned ? 'text-muted-foreground' : 'text-muted-foreground/75'}`}>
                {achievement.description}
              </p>
              
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <Badge variant="outline" className={`text-xs ${getRarityColor(achievement.rarity)}`}>
                    {achievement.rarity}
                  </Badge>
                  <Badge variant="secondary" className="text-xs">
                    {achievement.points} pts
                  </Badge>
                </div>
                
                {isEarned && userAchievement?.earnedAt && (
                  <div className="text-xs text-muted-foreground">
                    {formatDistanceToNow(new Date(userAchievement.earnedAt), { addSuffix: true })}
                  </div>
                )}
              </div>
              
              {!isEarned && progress > 0 && progress < 100 && (
                <div className="mt-2">
                  <div className="flex items-center justify-between text-xs mb-1">
                    <span>Progress</span>
                    <span>{progress.toFixed(0)}%</span>
                  </div>
                  <Progress value={progress} className="h-1" />
                </div>
              )}
            </div>
          </div>
        </CardContent>
      </Card>
    );
  };

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Trophy className="h-5 w-5" />
            Achievements
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center h-32">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (compact) {
    return (
      <Card>
        <CardHeader className="pb-3">
          <CardTitle className="text-lg flex items-center gap-2">
            <Trophy className="h-5 w-5" />
            Recent Achievements
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {userAchievements.slice(0, 3).map((userAchievement) => (
              <div key={userAchievement.id} className="flex items-center gap-3 p-2 rounded-lg bg-muted/50">
                <div className="p-1 rounded-full bg-primary/10">
                  {getTypeIcon(userAchievement.achievement.type)}
                </div>
                <div className="flex-1 min-w-0">
                  <div className="font-medium text-sm truncate">
                    {userAchievement.achievement.name}
                  </div>
                  <div className="text-xs text-muted-foreground">
                    {formatDistanceToNow(new Date(userAchievement.earnedAt), { addSuffix: true })}
                  </div>
                </div>
                <Badge variant="outline" className="text-xs">
                  {userAchievement.achievement.points} pts
                </Badge>
              </div>
            ))}
            
            {userAchievements.length === 0 && (
              <div className="text-center py-4 text-muted-foreground">
                <Trophy className="h-8 w-8 mx-auto mb-2 opacity-50" />
                <p className="text-sm">No achievements yet</p>
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Stats Overview */}
      {stats && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card>
            <CardContent className="p-4 text-center">
              <div className="text-2xl font-bold text-primary">{stats.totalEarned}</div>
              <div className="text-sm text-muted-foreground">Achievements Earned</div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4 text-center">
              <div className="text-2xl font-bold text-blue-600">{stats.totalPoints}</div>
              <div className="text-sm text-muted-foreground">Total Points</div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4 text-center">
              <div className="text-2xl font-bold text-green-600">
                {stats.totalAvailable > 0 ? Math.round((stats.totalEarned / stats.totalAvailable) * 100) : 0}%
              </div>
              <div className="text-sm text-muted-foreground">Completion Rate</div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4 text-center">
              <div className="text-2xl font-bold text-purple-600">
                {Object.keys(stats.byCategory).length}
              </div>
              <div className="text-sm text-muted-foreground">Categories</div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Achievements Tabs */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Trophy className="h-5 w-5" />
            Achievements
          </CardTitle>
          <CardDescription>
            Track your learning progress and unlock achievements
          </CardDescription>
        </CardHeader>
        
        <CardContent>
          <Tabs value={activeTab} onValueChange={setActiveTab}>
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="earned">
                Earned ({userAchievements.length})
              </TabsTrigger>
              <TabsTrigger value="available">
                Available ({availableAchievements.filter(a => !a.isEarned).length})
              </TabsTrigger>
              <TabsTrigger value="all">
                All ({availableAchievements.length})
              </TabsTrigger>
            </TabsList>
            
            <TabsContent value="earned" className="mt-4">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {userAchievements.map((userAchievement) => (
                  <AchievementCard
                    key={userAchievement.id}
                    achievement={userAchievement.achievement}
                    userAchievement={userAchievement}
                  />
                ))}
                
                {userAchievements.length === 0 && (
                  <div className="col-span-full text-center py-8 text-muted-foreground">
                    <Trophy className="h-12 w-12 mx-auto mb-4 opacity-50" />
                    <p>No achievements earned yet</p>
                    <p className="text-sm">Complete tutorials to start earning achievements!</p>
                  </div>
                )}
              </div>
            </TabsContent>
            
            <TabsContent value="available" className="mt-4">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {availableAchievements
                  .filter(achievement => !achievement.isEarned)
                  .map((achievement) => (
                    <AchievementCard
                      key={achievement.id}
                      achievement={achievement}
                    />
                  ))}
                
                {availableAchievements.filter(a => !a.isEarned).length === 0 && (
                  <div className="col-span-full text-center py-8 text-muted-foreground">
                    <Star className="h-12 w-12 mx-auto mb-4 opacity-50" />
                    <p>All achievements unlocked!</p>
                    <p className="text-sm">You've earned every available achievement.</p>
                  </div>
                )}
              </div>
            </TabsContent>
            
            <TabsContent value="all" className="mt-4">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {availableAchievements.map((achievement) => {
                  const userAchievement = userAchievements.find(
                    ua => ua.achievement.id === achievement.id
                  );
                  
                  return (
                    <AchievementCard
                      key={achievement.id}
                      achievement={achievement}
                      userAchievement={userAchievement}
                    />
                  );
                })}
              </div>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  );
}
