import { prisma } from "@/lib/database";
import crypto from "crypto";

export interface CertificateData {
  userId: string;
  tutorialId: string;
  recipientName: string;
  recipientEmail: string;
  completionDate: Date;
  completionTime: number;
  finalScore?: number;
  achievements?: string[];
  templateId?: string;
}

export interface CertificateTemplate {
  id: string;
  name: string;
  layout: string;
  colorScheme: string;
  titleTemplate: string;
  bodyTemplate: string;
  footerTemplate?: string;
  showLogo: boolean;
  showDate: boolean;
  showScore: boolean;
  showDuration: boolean;
  showSignature: boolean;
  logoUrl?: string;
  backgroundUrl?: string;
}

export interface Achievement {
  id: string;
  name: string;
  description: string;
  category: string;
  type: string;
  iconUrl?: string;
  badgeColor: string;
  criteria: any;
  points: number;
  rarity: string;
}

export class CertificateService {
  /**
   * Generate a certificate for tutorial completion
   */
  static async generateCertificate(data: CertificateData): Promise<any> {
    // Get tutorial information
    const tutorial = await prisma.tutorial.findUnique({
      where: { id: data.tutorialId },
      include: {
        creator: {
          select: {
            name: true,
            email: true,
          },
        },
      },
    });

    if (!tutorial) {
      throw new Error("Tutorial not found");
    }

    // Get or create default template
    let template = null;
    if (data.templateId) {
      template = await prisma.certificateTemplate.findUnique({
        where: { id: data.templateId },
      });
    }

    if (!template) {
      template = await this.getDefaultTemplate();
    }

    // Generate unique certificate number and verification code
    const certificateNumber = this.generateCertificateNumber();
    const verificationCode = this.generateVerificationCode();

    // Check for existing certificate
    if (data.userId) {
      const existingCertificate = await prisma.certificate.findFirst({
      where: {
        userId: data.userId,
        tutorialId: data.tutorialId,
        isRevoked: false,
      },
      });

      if (existingCertificate) {
        throw new Error("Certificate already exists for this tutorial");
      }
    }

    // Create certificate
    const certificate = await prisma.certificate.create({
      data: {
        userId: data.userId,
        tutorialId: data.tutorialId,
        certificateNumber,
        verificationCode,
        title: this.processTemplate(template.titleTemplate, {
          tutorialTitle: tutorial.title,
          recipientName: data.recipientName,
          completionDate: data.completionDate,
        }),
        description: this.processTemplate(template.bodyTemplate, {
          tutorialTitle: tutorial.title,
          recipientName: data.recipientName,
          completionDate: data.completionDate,
          completionTime: data.completionTime,
          finalScore: data.finalScore,
        }),
        templateId: template.id,
        recipientName: data.recipientName,
        recipientEmail: data.recipientEmail,
        issuerName: tutorial.creator.name || tutorial.creator.email,
        issuerTitle: "Tutorial Creator",
        completionDate: data.completionDate,
        completionTime: data.completionTime,
        finalScore: data.finalScore,
        achievements: data.achievements || [],
        metadata: {
          tutorialCategory: (tutorial.metadata as any)?.category,
          tutorialDifficulty: (tutorial.metadata as any)?.difficulty,
          templateUsed: template.name,
        },
      },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
            image: true,
          },
        },
        tutorial: {
          select: {
            id: true,
            title: true,
            description: true,
            metadata: true,
          },
        },
        template: true,
      },
    });

    // Update template usage count
    await prisma.certificateTemplate.update({
      where: { id: template.id },
      data: {
        usageCount: {
          increment: 1,
        },
      },
    });

    // Check and award achievements
    await this.checkAndAwardAchievements(data.userId, data.tutorialId, {
      completionTime: data.completionTime,
      finalScore: data.finalScore,
    });

    // Log analytics
    await prisma.analytics.create({
      data: {
        userId: data.userId,
        tutorialId: data.tutorialId,
        action: "certificate_generated",
        metadata: {
          certificateId: certificate.id,
          certificateNumber,
          templateId: template.id,
          completionTime: data.completionTime,
          finalScore: data.finalScore,
        },
      },
    });

    return certificate;
  }

  /**
   * Verify a certificate by verification code
   */
  static async verifyCertificate(verificationCode: string): Promise<any> {
    const certificate = await prisma.certificate.findUnique({
      where: { verificationCode },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
            image: true,
          },
        },
        tutorial: {
          select: {
            id: true,
            title: true,
            description: true,
            metadata: true,
          },
        },
        template: true,
      },
    });

    if (!certificate) {
      throw new Error("Certificate not found");
    }

    if (certificate.isRevoked) {
      throw new Error("Certificate has been revoked");
    }

    if (certificate.expiresAt && certificate.expiresAt < new Date()) {
      throw new Error("Certificate has expired");
    }

    // Log verification
    await prisma.analytics.create({
      data: {
        userId: certificate.userId,
        tutorialId: certificate.tutorialId,
        action: "certificate_verified",
        metadata: {
          certificateId: certificate.id,
          verificationCode,
          verifiedAt: new Date().toISOString(),
        },
      },
    });

    return certificate;
  }

  /**
   * Get user's certificates
   */
  static async getUserCertificates(userId: string): Promise<any[]> {
    const certificates = await prisma.certificate.findMany({
      where: {
        userId,
        isRevoked: false,
      },
      include: {
        tutorial: {
          select: {
            id: true,
            title: true,
            description: true,
            metadata: true,
          },
        },
        template: true,
      },
      orderBy: {
        issuedAt: "desc",
      },
    });

    return certificates;
  }

  /**
   * Revoke a certificate
   */
  static async revokeCertificate(
    certificateId: string,
    revokedBy: string,
    reason?: string
  ): Promise<void> {
    await prisma.certificate.update({
      where: { id: certificateId },
      data: {
        isRevoked: true,
        revokedAt: new Date(),
        revokedBy,
        metadata: {
          revokeReason: reason,
        },
      },
    });

    // Log revocation
    await prisma.analytics.create({
      data: {
        userId: revokedBy,
        action: "certificate_revoked",
        metadata: {
          certificateId,
          reason,
        },
      },
    });
  }

  /**
   * Check and award achievements
   */
  static async checkAndAwardAchievements(
    userId: string,
    tutorialId: string,
    performance: {
      completionTime: number;
      finalScore?: number;
    }
  ): Promise<void> {
    // Get all active achievements
    const achievements = await prisma.achievement.findMany({
      where: { isActive: true },
    });

    for (const achievement of achievements) {
      const criteria = achievement.criteria as any;
      let shouldAward = false;

      switch (achievement.type) {
        case "completion":
          shouldAward = true; // Award for any completion
          break;

        case "speed":
          if (criteria.maxTime && performance.completionTime <= criteria.maxTime) {
            shouldAward = true;
          }
          break;

        case "accuracy":
          if (criteria.minScore && performance.finalScore && performance.finalScore >= criteria.minScore) {
            shouldAward = true;
          }
          break;

        case "streak":
          // Check completion streak
          const recentCompletions = await prisma.tutorialProgress.count({
            where: {
              userId,
              completed: true,
              completedAt: {
                gte: new Date(Date.now() - criteria.streakDays * 24 * 60 * 60 * 1000),
              },
            },
          });
          if (recentCompletions >= criteria.streakCount) {
            shouldAward = true;
          }
          break;

        case "milestone":
          // Check total completions
          const totalCompletions = await prisma.tutorialProgress.count({
            where: {
              userId,
              completed: true,
            },
          });
          if (totalCompletions >= criteria.completionCount) {
            shouldAward = true;
          }
          break;
      }

      if (shouldAward) {
        // Check if user already has this achievement
        const existingAchievement = await prisma.userAchievement.findUnique({
          where: {
            userId_achievementId_tutorialId: {
              userId,
              achievementId: achievement.id,
              tutorialId: achievement.type === "completion" ? tutorialId : null,
            },
          },
        });

        if (!existingAchievement) {
          await prisma.userAchievement.create({
            data: {
              userId,
              achievementId: achievement.id,
              tutorialId: achievement.type === "completion" ? tutorialId : null,
              progress: 100.0,
              metadata: {
                completionTime: performance.completionTime,
                finalScore: performance.finalScore,
              },
            },
          });

          // Log achievement
          await prisma.analytics.create({
            data: {
              userId,
              tutorialId,
              action: "achievement_earned",
              metadata: {
                achievementId: achievement.id,
                achievementName: achievement.name,
                achievementType: achievement.type,
                points: achievement.points,
              },
            },
          });
        }
      }
    }
  }

  /**
   * Get default certificate template
   */
  private static async getDefaultTemplate(): Promise<any> {
    let template = await prisma.certificateTemplate.findFirst({
      where: {
        isDefault: true,
        isActive: true,
      },
    });

    if (!template) {
      // Create default template if none exists
      template = await prisma.certificateTemplate.create({
        data: {
          name: "Default Certificate",
          description: "Default certificate template",
          isDefault: true,
          layout: "standard",
          colorScheme: "blue",
          titleTemplate: "Certificate of Completion",
          bodyTemplate: "This certifies that {recipientName} has successfully completed the tutorial \"{tutorialTitle}\" on {completionDate}.",
          footerTemplate: "Congratulations on your achievement!",
          createdBy: "system", // Would need a system user
        },
      });
    }

    return template;
  }

  /**
   * Generate unique certificate number
   */
  private static generateCertificateNumber(): string {
    const timestamp = Date.now().toString(36);
    const random = Math.random().toString(36).substr(2, 5);
    return `CERT-${timestamp}-${random}`.toUpperCase();
  }

  /**
   * Generate verification code
   */
  private static generateVerificationCode(): string {
    return crypto.randomBytes(16).toString("hex");
  }

  /**
   * Process template with variables
   */
  private static processTemplate(template: string, variables: any): string {
    let processed = template;
    
    Object.entries(variables).forEach(([key, value]) => {
      const placeholder = `{${key}}`;
      let replacement = "";
      
      if (value instanceof Date) {
        replacement = value.toLocaleDateString();
      } else if (typeof value === "number") {
        replacement = value.toString();
      } else if (value) {
        replacement = value.toString();
      }
      
      processed = processed.replace(new RegExp(placeholder, "g"), replacement);
    });
    
    return processed;
  }
}
