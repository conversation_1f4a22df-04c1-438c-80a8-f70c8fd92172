import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { prisma } from "@/lib/database";
import { CertificateService } from "@/lib/certificate-service";
import { z } from "zod";

const generateCertificateSchema = z.object({
  tutorialId: z.string(),
  templateId: z.string().optional(),
});

// GET /api/certificates - Get user's certificates
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession();

    if (!session?.user?.email) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const user = await prisma.user.findUnique({
      where: { email: session.user.email },
    });

    if (!user) {
      return NextResponse.json({ error: "User not found" }, { status: 404 });
    }

    const { searchParams } = new URL(request.url);
    const tutorialId = searchParams.get("tutorialId");

    let whereClause: any = {
      userId: user.id,
      isRevoked: false,
    };

    if (tutorialId) {
      whereClause.tutorialId = tutorialId;
    }

    const certificates = await prisma.certificate.findMany({
      where: whereClause,
      include: {
        tutorial: {
          select: {
            id: true,
            title: true,
            description: true,
            metadata: true,
          },
        },
        template: {
          select: {
            id: true,
            name: true,
            layout: true,
            colorScheme: true,
          },
        },
      },
      orderBy: {
        issuedAt: "desc",
      },
    });

    return NextResponse.json({
      certificates,
      total: certificates.length,
    });
  } catch (error) {
    console.error("Error fetching certificates:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

// POST /api/certificates - Generate a new certificate
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession();

    if (!session?.user?.email) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const user = await prisma.user.findUnique({
      where: { email: session.user.email },
    });

    if (!user) {
      return NextResponse.json({ error: "User not found" }, { status: 404 });
    }

    const body = await request.json();
    const validatedData = generateCertificateSchema.parse(body);

    // Check if user has completed the tutorial
    const tutorialProgress = await prisma.tutorialProgress.findUnique({
      where: {
        tutorialId_userId: {
          tutorialId: validatedData.tutorialId,
          userId: user.id,
        },
      },
    });

    if (!tutorialProgress || !tutorialProgress.completed) {
      return NextResponse.json(
        { error: "Tutorial not completed" },
        { status: 400 }
      );
    }

    // Check if certificate already exists
    const existingCertificate = await prisma.certificate.findFirst({
      where: {
        userId: user.id,
        tutorialId: validatedData.tutorialId,
        isRevoked: false,
      },
    });

    if (existingCertificate) {
      return NextResponse.json(
        { error: "Certificate already exists for this tutorial" },
        { status: 400 }
      );
    }

    // Generate certificate
    const certificate = await CertificateService.generateCertificate({
      userId: user.id,
      tutorialId: validatedData.tutorialId,
      recipientName: user.name || user.email,
      recipientEmail: user.email,
      completionDate: tutorialProgress.completedAt || new Date(),
      completionTime: tutorialProgress.timeSpent,
      finalScore: tutorialProgress.progress,
      templateId: validatedData.templateId,
    });

    return NextResponse.json({
      message: "Certificate generated successfully",
      certificate,
    });
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Invalid request data", details: error.errors },
        { status: 400 }
      );
    }

    console.error("Error generating certificate:", error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : "Internal server error" },
      { status: 500 }
    );
  }
}
