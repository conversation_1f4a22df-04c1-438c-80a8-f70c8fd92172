import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { prisma } from "@/lib/database";
import { AdaptiveLearningService } from "@/lib/adaptive-learning";
import crypto from "crypto";

// GET /api/tutorials/[id]/adaptive - Get adapted tutorial for user
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession();

    if (!session?.user?.email) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const user = await prisma.user.findUnique({
      where: { email: session.user.email },
    });

    if (!user) {
      return NextResponse.json({ error: "User not found" }, { status: 404 });
    }

    const tutorialId = params.id;

    // Check if tutorial exists
    const tutorial = await prisma.tutorial.findUnique({
      where: { id: tutorialId },
    });

    if (!tutorial) {
      return NextResponse.json({ error: "Tutorial not found" }, { status: 404 });
    }

    // Get user's learning profile
    const profile = await AdaptiveLearningService.getUserLearningProfile(user.id);
    
    if (!profile || !profile.adaptiveEnabled) {
      // Return original tutorial if adaptive learning is disabled
      return NextResponse.json({
        tutorial,
        adaptations: [],
        confidence: 0,
        reasoning: ["Adaptive learning is disabled for this user"],
        isAdapted: false,
      });
    }

    // Adapt tutorial based on user profile
    const adaptationResult = await AdaptiveLearningService.adaptTutorial(tutorialId, user.id);

    // Create adaptive session record
    const sessionId = `adaptive-${Date.now()}-${crypto.randomUUID().slice(0, 8)}`;
    
    await prisma.adaptiveTutorialSession.create({
      data: {
        userId: user.id,
        tutorialId,
        sessionId,
        adaptations: adaptationResult.adaptations,
        originalDifficulty: (tutorial.metadata as any)?.difficulty || "beginner",
        adaptedDifficulty: (adaptationResult.adaptedTutorial.metadata as any)?.difficulty,
        originalPace: "medium", // Default
        adaptedPace: profile.preferredPace,
      },
    });

    // Log analytics
    await prisma.analytics.create({
      data: {
        userId: user.id,
        tutorialId,
        action: "tutorial_adapted",
        metadata: {
          sessionId,
          adaptationCount: adaptationResult.adaptations.length,
          confidence: adaptationResult.confidence,
          adaptations: adaptationResult.adaptations.map(a => ({
            type: a.type,
            reason: a.reason,
          })),
        },
      },
    });

    return NextResponse.json({
      tutorial: adaptationResult.adaptedTutorial,
      adaptations: adaptationResult.adaptations,
      confidence: adaptationResult.confidence,
      reasoning: adaptationResult.reasoning,
      isAdapted: true,
      sessionId,
    });
  } catch (error) {
    console.error("Error adapting tutorial:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

// POST /api/tutorials/[id]/adaptive/feedback - Provide feedback on adaptation effectiveness
export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession();

    if (!session?.user?.email) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const user = await prisma.user.findUnique({
      where: { email: session.user.email },
    });

    if (!user) {
      return NextResponse.json({ error: "User not found" }, { status: 404 });
    }

    const tutorialId = params.id;
    const body = await request.json();
    
    const { 
      sessionId, 
      satisfactionRating, 
      adaptationEffectiveness, 
      completionTime,
      hintsProvided,
      errorsEncountered,
      retriesUsed,
      stepsSkipped,
      feedback 
    } = body;

    if (!sessionId) {
      return NextResponse.json({ error: "Session ID is required" }, { status: 400 });
    }

    // Update adaptive session with feedback
    const updatedSession = await prisma.adaptiveTutorialSession.update({
      where: { sessionId },
      data: {
        satisfactionRating,
        adaptationEffectiveness,
        completionTime,
        hintsProvided: hintsProvided || 0,
        errorsEncountered: errorsEncountered || 0,
        retriesUsed: retriesUsed || 0,
        stepsSkipped: stepsSkipped || 0,
        completedAt: new Date(),
      },
    });

    // Update user learning profile based on this session
    if (completionTime && errorsEncountered !== undefined && retriesUsed !== undefined) {
      const errorRate = errorsEncountered / Math.max(1, completionTime / 60); // errors per minute
      const retryRate = retriesUsed / Math.max(1, completionTime / 60); // retries per minute

      await AdaptiveLearningService.updateLearningProfile(user.id, {
        tutorialId,
        completionTime,
        errorRate,
        retryRate,
        hintsUsed: hintsProvided || 0,
        stepsSkipped: stepsSkipped || 0,
        satisfactionRating,
      });
    }

    // Log feedback analytics
    await prisma.analytics.create({
      data: {
        userId: user.id,
        tutorialId,
        action: "adaptive_feedback",
        metadata: {
          sessionId,
          satisfactionRating,
          adaptationEffectiveness,
          feedback,
          performance: {
            completionTime,
            hintsProvided,
            errorsEncountered,
            retriesUsed,
            stepsSkipped,
          },
        },
      },
    });

    return NextResponse.json({
      message: "Feedback recorded successfully",
      session: updatedSession,
    });
  } catch (error) {
    console.error("Error recording adaptive feedback:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
