"use client";

import React, { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Progress } from "@/components/ui/progress";
import {
  BarC<PERSON>,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  LineChart,
  Line,
  PieChart,
  Pie,
  Cell,
} from "recharts";
import {
  BarChart3,
  Users,
  Clock,
  CheckCircle,
  TrendingUp,
  TrendingDown,
  Eye,
  Star,
  AlertTriangle,
  Globe,
  Smartphone,
  Monitor,
  Download,
} from "lucide-react";
import { formatDistanceToNow } from "date-fns";

interface TutorialAnalyticsProps {
  tutorialId: string;
}

interface AnalyticsData {
  overview: {
    totalViews: number;
    uniqueUsers: number;
    completions: number;
    completionRate: number;
    averageCompletionTime: number;
    averageRating: number;
    totalRatings: number;
  };
  dropOffAnalytics: Array<{
    step: number;
    dropOffs: number;
  }>;
  timeSeriesData: Array<{
    date: string;
    action: string;
    count: number;
  }>;
  stepAnalytics?: Array<{
    stepIndex: number;
    stepId: string;
    stepTitle: string;
    views: number;
    completions: number;
    errors: number;
    completionRate: number;
    errorRate: number;
  }>;
  engagement: {
    avg_time_per_session: number;
    avg_steps_per_session: number;
    active_users: number;
  };
  deviceAnalytics: Array<{
    device_type: string;
    browser: string;
    count: number;
  }>;
  geoAnalytics: Array<{
    country: string;
    region: string;
    count: number;
  }>;
  timeRange: string;
  dateRange: {
    start: string;
    end: string;
  };
}

export default function TutorialAnalyticsDashboard({ tutorialId }: TutorialAnalyticsProps) {
  const [analytics, setAnalytics] = useState<AnalyticsData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [timeRange, setTimeRange] = useState("30d");
  const [includeStepAnalytics, setIncludeStepAnalytics] = useState(false);
  const [error, setError] = useState("");

  useEffect(() => {
    loadAnalytics();
  }, [tutorialId, timeRange, includeStepAnalytics]);

  const loadAnalytics = async () => {
    try {
      setIsLoading(true);
      setError("");
      
      const params = new URLSearchParams({
        timeRange,
        includeSteps: includeStepAnalytics.toString(),
      });

      const response = await fetch(`/api/tutorials/${tutorialId}/analytics?${params}`);
      
      if (response.ok) {
        const data = await response.json();
        setAnalytics(data);
      } else {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to load analytics");
      }
    } catch (error) {
      console.error("Error loading analytics:", error);
      setError(error instanceof Error ? error.message : "Failed to load analytics");
    } finally {
      setIsLoading(false);
    }
  };

  const exportAnalytics = async () => {
    try {
      const params = new URLSearchParams({
        timeRange,
        includeSteps: includeStepAnalytics.toString(),
        format: "csv",
      });

      const response = await fetch(`/api/tutorials/${tutorialId}/analytics/export?${params}`);
      
      if (response.ok) {
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement("a");
        a.href = url;
        a.download = `tutorial-${tutorialId}-analytics-${timeRange}.csv`;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);
      }
    } catch (error) {
      console.error("Error exporting analytics:", error);
    }
  };

  const formatTimeSeriesData = (data: any[]) => {
    const grouped = data.reduce((acc, item) => {
      const date = new Date(item.date).toLocaleDateString();
      if (!acc[date]) {
        acc[date] = { date, views: 0, completions: 0 };
      }
      if (item.action === "tutorial_viewed") {
        acc[date].views = item.count;
      } else if (item.action === "tutorial_completed") {
        acc[date].completions = item.count;
      }
      return acc;
    }, {});

    return Object.values(grouped);
  };

  const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8'];

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <BarChart3 className="h-5 w-5" />
            Tutorial Analytics
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center h-64">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <BarChart3 className="h-5 w-5" />
            Tutorial Analytics
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8">
            <AlertTriangle className="h-12 w-12 text-destructive mx-auto mb-4" />
            <p className="text-destructive">{error}</p>
            <Button onClick={loadAnalytics} className="mt-4">
              Retry
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!analytics) return null;

  return (
    <div className="space-y-6">
      {/* Controls */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <BarChart3 className="h-5 w-5" />
                Tutorial Analytics
              </CardTitle>
              <CardDescription>
                Performance insights and user engagement metrics
              </CardDescription>
            </div>
            <div className="flex items-center gap-2">
              <Select value={timeRange} onValueChange={setTimeRange}>
                <SelectTrigger className="w-32">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="7d">Last 7 days</SelectItem>
                  <SelectItem value="30d">Last 30 days</SelectItem>
                  <SelectItem value="90d">Last 90 days</SelectItem>
                  <SelectItem value="1y">Last year</SelectItem>
                </SelectContent>
              </Select>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setIncludeStepAnalytics(!includeStepAnalytics)}
              >
                {includeStepAnalytics ? "Hide" : "Show"} Step Details
              </Button>
              <Button variant="outline" size="sm" onClick={exportAnalytics} className="gap-1">
                <Download className="h-4 w-4" />
                Export
              </Button>
            </div>
          </div>
        </CardHeader>
      </Card>

      {/* Overview Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Total Views</p>
                <p className="text-2xl font-bold">{analytics.overview.totalViews.toLocaleString()}</p>
              </div>
              <Eye className="h-8 w-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Unique Users</p>
                <p className="text-2xl font-bold">{analytics.overview.uniqueUsers.toLocaleString()}</p>
              </div>
              <Users className="h-8 w-8 text-green-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Completions</p>
                <p className="text-2xl font-bold">{analytics.overview.completions.toLocaleString()}</p>
                <p className="text-xs text-muted-foreground">
                  {analytics.overview.completionRate.toFixed(1)}% completion rate
                </p>
              </div>
              <CheckCircle className="h-8 w-8 text-emerald-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Avg. Time</p>
                <p className="text-2xl font-bold">{analytics.overview.averageCompletionTime}m</p>
                <div className="flex items-center gap-1 mt-1">
                  <Star className="h-3 w-3 text-yellow-500" />
                  <span className="text-xs text-muted-foreground">
                    {analytics.overview.averageRating.toFixed(1)} ({analytics.overview.totalRatings} ratings)
                  </span>
                </div>
              </div>
              <Clock className="h-8 w-8 text-orange-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Time Series Chart */}
      <Card>
        <CardHeader>
          <CardTitle>Views and Completions Over Time</CardTitle>
        </CardHeader>
        <CardContent>
          <ResponsiveContainer width="100%" height={300}>
            <LineChart data={formatTimeSeriesData(analytics.timeSeriesData)}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="date" />
              <YAxis />
              <Tooltip />
              <Line type="monotone" dataKey="views" stroke="#8884d8" name="Views" />
              <Line type="monotone" dataKey="completions" stroke="#82ca9d" name="Completions" />
            </LineChart>
          </ResponsiveContainer>
        </CardContent>
      </Card>

      {/* Drop-off Analysis */}
      {analytics.dropOffAnalytics.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>Drop-off Analysis</CardTitle>
            <CardDescription>
              Where users are leaving the tutorial
            </CardDescription>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <BarChart data={analytics.dropOffAnalytics}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="step" />
                <YAxis />
                <Tooltip />
                <Bar dataKey="dropOffs" fill="#ff6b6b" name="Drop-offs" />
              </BarChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>
      )}

      {/* Step Analytics */}
      {includeStepAnalytics && analytics.stepAnalytics && (
        <Card>
          <CardHeader>
            <CardTitle>Step-by-Step Performance</CardTitle>
            <CardDescription>
              Detailed analytics for each tutorial step
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {analytics.stepAnalytics.map((step) => (
                <div key={step.stepIndex} className="border rounded-lg p-4">
                  <div className="flex items-center justify-between mb-2">
                    <h4 className="font-medium">
                      Step {step.stepIndex + 1}: {step.stepTitle}
                    </h4>
                    <div className="flex items-center gap-2">
                      <Badge variant="outline">{step.views} views</Badge>
                      <Badge variant="outline">{step.completions} completions</Badge>
                      {step.errors > 0 && (
                        <Badge variant="destructive">{step.errors} errors</Badge>
                      )}
                    </div>
                  </div>
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <div className="flex items-center justify-between text-sm mb-1">
                        <span>Completion Rate</span>
                        <span>{step.completionRate.toFixed(1)}%</span>
                      </div>
                      <Progress value={step.completionRate} className="h-2" />
                    </div>
                    <div>
                      <div className="flex items-center justify-between text-sm mb-1">
                        <span>Error Rate</span>
                        <span>{step.errorRate.toFixed(1)}%</span>
                      </div>
                      <Progress 
                        value={step.errorRate} 
                        className="h-2"
                        // @ts-ignore
                        indicatorClassName="bg-destructive"
                      />
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Device and Geographic Analytics */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Device Analytics */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Monitor className="h-5 w-5" />
              Device Usage
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {analytics.deviceAnalytics.slice(0, 5).map((device, index) => (
                <div key={index} className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    {device.device_type === 'mobile' ? (
                      <Smartphone className="h-4 w-4" />
                    ) : (
                      <Monitor className="h-4 w-4" />
                    )}
                    <span className="text-sm">
                      {device.device_type} - {device.browser}
                    </span>
                  </div>
                  <Badge variant="outline">{device.count}</Badge>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Geographic Analytics */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Globe className="h-5 w-5" />
              Geographic Distribution
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {analytics.geoAnalytics.slice(0, 5).map((geo, index) => (
                <div key={index} className="flex items-center justify-between">
                  <span className="text-sm">
                    {geo.country}{geo.region && `, ${geo.region}`}
                  </span>
                  <Badge variant="outline">{geo.count}</Badge>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Engagement Metrics */}
      <Card>
        <CardHeader>
          <CardTitle>Engagement Metrics</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600">
                {Math.round(analytics.engagement.avg_time_per_session / 60)}m
              </div>
              <div className="text-sm text-muted-foreground">Avg. Session Time</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">
                {Math.round(analytics.engagement.avg_steps_per_session)}
              </div>
              <div className="text-sm text-muted-foreground">Avg. Steps per Session</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-purple-600">
                {analytics.engagement.active_users}
              </div>
              <div className="text-sm text-muted-foreground">Active Users</div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
