import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { prisma } from "@/lib/database";
import { z } from "zod";

const searchSchema = z.object({
  query: z.string().optional(),
  category: z.string().optional(),
  difficulty: z.enum(["beginner", "intermediate", "advanced"]).optional(),
  language: z.string().optional(),
  tags: z.array(z.string()).optional(),
  minDuration: z.number().optional(),
  maxDuration: z.number().optional(),
  minRating: z.number().min(1).max(5).optional(),
  sortBy: z.enum(["relevance", "created", "updated", "rating", "popularity", "duration"]).default("relevance"),
  sortOrder: z.enum(["asc", "desc"]).default("desc"),
  page: z.number().min(1).default(1),
  limit: z.number().min(1).max(50).default(20),
  includeInactive: z.boolean().default(false),
  userId: z.string().optional(), // For personalized results
});

// GET /api/tutorials/search - Advanced tutorial search
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession();
    const { searchParams } = new URL(request.url);

    // Parse search parameters
    const searchData = {
      query: searchParams.get("query") || undefined,
      category: searchParams.get("category") || undefined,
      difficulty: searchParams.get("difficulty") as any || undefined,
      language: searchParams.get("language") || undefined,
      tags: searchParams.get("tags")?.split(",").filter(Boolean) || undefined,
      minDuration: searchParams.get("minDuration") ? parseInt(searchParams.get("minDuration")!) : undefined,
      maxDuration: searchParams.get("maxDuration") ? parseInt(searchParams.get("maxDuration")!) : undefined,
      minRating: searchParams.get("minRating") ? parseFloat(searchParams.get("minRating")!) : undefined,
      sortBy: searchParams.get("sortBy") as any || "relevance",
      sortOrder: searchParams.get("sortOrder") as any || "desc",
      page: searchParams.get("page") ? parseInt(searchParams.get("page")!) : 1,
      limit: searchParams.get("limit") ? parseInt(searchParams.get("limit")!) : 20,
      includeInactive: searchParams.get("includeInactive") === "true",
      userId: searchParams.get("userId") || undefined,
    };

    const validatedData = searchSchema.parse(searchData);

    // Get current user for personalization
    let currentUser = null;
    if (session?.user?.email) {
      currentUser = await prisma.user.findUnique({
        where: { email: session.user.email },
        include: {
          learningProfile: true,
        },
      });
    }

    // Build search query
    const whereClause: any = {};

    // Active status filter
    if (!validatedData.includeInactive) {
      whereClause.isActive = true;
      whereClause.status = "published";
    }

    // Text search
    if (validatedData.query) {
      whereClause.OR = [
        { title: { contains: validatedData.query, mode: "insensitive" } },
        { description: { contains: validatedData.query, mode: "insensitive" } },
        {
          metadata: {
            path: ["tags"],
            array_contains: validatedData.query,
          },
        },
      ];
    }

    // Category filter
    if (validatedData.category) {
      whereClause.metadata = {
        ...whereClause.metadata,
        path: ["category"],
        equals: validatedData.category,
      };
    }

    // Difficulty filter
    if (validatedData.difficulty) {
      whereClause.metadata = {
        ...whereClause.metadata,
        path: ["difficulty"],
        equals: validatedData.difficulty,
      };
    }

    // Language filter
    if (validatedData.language) {
      whereClause.language = validatedData.language;
    }

    // Tags filter
    if (validatedData.tags && validatedData.tags.length > 0) {
      whereClause.tags = {
        hasSome: validatedData.tags,
      };
    }

    // Duration filter
    if (validatedData.minDuration || validatedData.maxDuration) {
      const durationFilter: any = {};
      if (validatedData.minDuration) {
        durationFilter.gte = validatedData.minDuration;
      }
      if (validatedData.maxDuration) {
        durationFilter.lte = validatedData.maxDuration;
      }
      whereClause.estimatedDuration = durationFilter;
    }

    // Rating filter (would need to calculate from analytics)
    if (validatedData.minRating) {
      // This would require a more complex query to filter by average rating
      // For now, we'll skip this filter
    }

    // Build order by clause
    let orderBy: any = {};
    switch (validatedData.sortBy) {
      case "created":
        orderBy = { createdAt: validatedData.sortOrder };
        break;
      case "updated":
        orderBy = { updatedAt: validatedData.sortOrder };
        break;
      case "rating":
        // Would need to order by calculated average rating
        orderBy = { createdAt: "desc" }; // Fallback
        break;
      case "popularity":
        // Would need to order by view count or completion count
        orderBy = { createdAt: "desc" }; // Fallback
        break;
      case "duration":
        orderBy = { estimatedDuration: validatedData.sortOrder };
        break;
      case "relevance":
      default:
        // For relevance, we'll use a combination of factors
        orderBy = [
          { updatedAt: "desc" },
          { createdAt: "desc" },
        ];
        break;
    }

    // Calculate pagination
    const skip = (validatedData.page - 1) * validatedData.limit;

    // Execute search query
    const [tutorials, totalCount] = await Promise.all([
      prisma.tutorial.findMany({
        where: whereClause,
        include: {
          creator: {
            select: {
              id: true,
              name: true,
              email: true,
              image: true,
            },
          },
          _count: {
            select: {
              progress: true,
              analytics: {
                where: {
                  action: "tutorial_viewed",
                },
              },
            },
          },
        },
        orderBy,
        skip,
        take: validatedData.limit,
      }),
      prisma.tutorial.count({ where: whereClause }),
    ]);

    // Enhance results with analytics and personalization
    const enhancedTutorials = await Promise.all(
      tutorials.map(async (tutorial) => {
        // Get analytics data
        const analytics = await prisma.analytics.aggregate({
          where: {
            tutorialId: tutorial.id,
            action: "tutorial_viewed",
          },
          _count: { id: true },
        });

        const completions = await prisma.tutorialProgress.count({
          where: {
            tutorialId: tutorial.id,
            completed: true,
          },
        });

        const averageRating = await prisma.analytics.aggregate({
          where: {
            tutorialId: tutorial.id,
            action: "tutorial_feedback",
            metadata: {
              path: ["rating"],
              not: null,
            },
          },
          _avg: {
            // This would need a custom query to extract rating from JSON metadata
          },
        });

        // Calculate relevance score for personalization
        let relevanceScore = 0;
        if (currentUser?.learningProfile) {
          const profile = currentUser.learningProfile;
          const tutorialMetadata = tutorial.metadata as any;

          // Category preference
          if (profile.preferredCategories.includes(tutorialMetadata.category)) {
            relevanceScore += 10;
          }

          // Difficulty match
          if (profile.difficultyLevel === tutorialMetadata.difficulty) {
            relevanceScore += 5;
          }

          // Duration preference
          const estimatedTime = tutorialMetadata.estimatedTime || 0;
          if (estimatedTime <= profile.sessionDuration / 60) {
            relevanceScore += 3;
          }

          // Strong topics
          if (profile.strongTopics.some(topic => 
            tutorial.tags?.includes(topic) || tutorialMetadata.category === topic
          )) {
            relevanceScore += 7;
          }

          // Weak topics (learning opportunities)
          if (profile.weakTopics.some(topic => 
            tutorial.tags?.includes(topic) || tutorialMetadata.category === topic
          )) {
            relevanceScore += 15; // Higher score for learning opportunities
          }
        }

        return {
          ...tutorial,
          analytics: {
            views: analytics._count.id,
            completions,
            averageRating: 0, // Would calculate from feedback
          },
          relevanceScore,
          isRecommended: relevanceScore > 10,
        };
      })
    );

    // Sort by relevance score if relevance sorting is selected
    if (validatedData.sortBy === "relevance" && currentUser?.learningProfile) {
      enhancedTutorials.sort((a, b) => b.relevanceScore - a.relevanceScore);
    }

    // Get search suggestions and filters
    const [categories, difficulties, languages] = await Promise.all([
      prisma.tutorial.groupBy({
        by: ["metadata"],
        where: { isActive: true, status: "published" },
        _count: { id: true },
      }).then(results => 
        results
          .map(r => ({ category: (r.metadata as any)?.category, count: r._count.id }))
          .filter(r => r.category)
          .reduce((acc: any[], curr) => {
            const existing = acc.find(item => item.category === curr.category);
            if (existing) {
              existing.count += curr.count;
            } else {
              acc.push(curr);
            }
            return acc;
          }, [])
          .sort((a, b) => b.count - a.count)
      ),
      
      prisma.tutorial.groupBy({
        by: ["metadata"],
        where: { isActive: true, status: "published" },
        _count: { id: true },
      }).then(results => 
        results
          .map(r => ({ difficulty: (r.metadata as any)?.difficulty, count: r._count.id }))
          .filter(r => r.difficulty)
          .reduce((acc: any[], curr) => {
            const existing = acc.find(item => item.difficulty === curr.difficulty);
            if (existing) {
              existing.count += curr.count;
            } else {
              acc.push(curr);
            }
            return acc;
          }, [])
          .sort((a, b) => b.count - a.count)
      ),

      prisma.tutorial.groupBy({
        by: ["language"],
        where: { isActive: true, status: "published" },
        _count: { id: true },
      }).then(results => 
        results.map(r => ({ language: r.language, count: r._count.id }))
          .sort((a, b) => b.count - a.count)
      ),
    ]);

    // Log search analytics
    if (currentUser) {
      await prisma.analytics.create({
        data: {
          userId: currentUser.id,
          action: "tutorial_search",
          metadata: {
            query: validatedData.query,
            filters: {
              category: validatedData.category,
              difficulty: validatedData.difficulty,
              language: validatedData.language,
              tags: validatedData.tags,
            },
            resultsCount: enhancedTutorials.length,
            page: validatedData.page,
          },
        },
      });
    }

    return NextResponse.json({
      tutorials: enhancedTutorials,
      pagination: {
        page: validatedData.page,
        limit: validatedData.limit,
        total: totalCount,
        pages: Math.ceil(totalCount / validatedData.limit),
      },
      filters: {
        categories,
        difficulties,
        languages,
      },
      searchQuery: validatedData.query,
      appliedFilters: {
        category: validatedData.category,
        difficulty: validatedData.difficulty,
        language: validatedData.language,
        tags: validatedData.tags,
        minDuration: validatedData.minDuration,
        maxDuration: validatedData.maxDuration,
        minRating: validatedData.minRating,
      },
    });
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Invalid search parameters", details: error.errors },
        { status: 400 }
      );
    }

    console.error("Error searching tutorials:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
