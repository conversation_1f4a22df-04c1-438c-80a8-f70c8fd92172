import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { prisma } from "@/lib/database";

// GET /api/achievements - Get user's achievements
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession();

    if (!session?.user?.email) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const user = await prisma.user.findUnique({
      where: { email: session.user.email },
    });

    if (!user) {
      return NextResponse.json({ error: "User not found" }, { status: 404 });
    }

    const { searchParams } = new URL(request.url);
    const category = searchParams.get("category");
    const type = searchParams.get("type");
    const includeProgress = searchParams.get("includeProgress") === "true";

    // Get user's achievements
    const userAchievements = await prisma.userAchievement.findMany({
      where: {
        userId: user.id,
        ...(category && {
          achievement: {
            category,
          },
        }),
        ...(type && {
          achievement: {
            type,
          },
        }),
      },
      include: {
        achievement: true,
        tutorial: {
          select: {
            id: true,
            title: true,
            metadata: true,
          },
        },
      },
      orderBy: {
        earnedAt: "desc",
      },
    });

    // Get all available achievements for progress tracking
    let allAchievements = [];
    if (includeProgress) {
      allAchievements = await prisma.achievement.findMany({
        where: {
          isActive: true,
          ...(category && { category }),
          ...(type && { type }),
        },
        orderBy: {
          category: "asc",
        },
      });
    }

    // Calculate achievement statistics
    const stats = {
      totalEarned: userAchievements.length,
      totalAvailable: includeProgress ? allAchievements.length : 0,
      totalPoints: userAchievements.reduce((sum, ua) => sum + ua.achievement.points, 0),
      byCategory: userAchievements.reduce((acc: any, ua) => {
        const cat = ua.achievement.category;
        acc[cat] = (acc[cat] || 0) + 1;
        return acc;
      }, {}),
      byRarity: userAchievements.reduce((acc: any, ua) => {
        const rarity = ua.achievement.rarity;
        acc[rarity] = (acc[rarity] || 0) + 1;
        return acc;
      }, {}),
    };

    // Format response
    const response: any = {
      achievements: userAchievements.map(ua => ({
        id: ua.id,
        earnedAt: ua.earnedAt,
        progress: ua.progress,
        metadata: ua.metadata,
        achievement: {
          id: ua.achievement.id,
          name: ua.achievement.name,
          description: ua.achievement.description,
          category: ua.achievement.category,
          type: ua.achievement.type,
          iconUrl: ua.achievement.iconUrl,
          badgeColor: ua.achievement.badgeColor,
          points: ua.achievement.points,
          rarity: ua.achievement.rarity,
        },
        tutorial: ua.tutorial,
      })),
      stats,
    };

    if (includeProgress) {
      response.availableAchievements = allAchievements.map(achievement => {
        const userAchievement = userAchievements.find(ua => ua.achievementId === achievement.id);
        return {
          ...achievement,
          isEarned: !!userAchievement,
          earnedAt: userAchievement?.earnedAt,
          progress: userAchievement?.progress || 0,
        };
      });
    }

    return NextResponse.json(response);
  } catch (error) {
    console.error("Error fetching achievements:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
