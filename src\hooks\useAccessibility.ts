import React, { useEffect, useRef, useCallback, useState } from 'react';
import { 
  FocusManager, 
  generateId, 
  announceToScreen<PERSON>eader,
  prefersReducedMotion,
  prefersHigh<PERSON>ontrast,
  KeyboardKeys,
  handleKeyboardNavigation
} from '@/lib/accessibility';

// Hook for managing focus trap
export function useFocusTrap(isActive: boolean = true) {
  const containerRef = useRef<HTMLElement>(null);
  const cleanupRef = useRef<(() => void) | null>(null);

  useEffect(() => {
    if (isActive && containerRef.current) {
      cleanupRef.current = FocusManager.trapFocus(containerRef.current) || null;
    }

    return () => {
      if (cleanupRef.current) {
        cleanupRef.current();
        cleanupRef.current = null;
      }
    };
  }, [isActive]);

  return containerRef;
}

// Hook for managing focus restoration
export function useFocusRestore() {
  useEffect(() => {
    FocusManager.saveFocus();
    return () => {
      FocusManager.restoreFocus();
    };
  }, []);
}

// Hook for generating stable IDs
export function useId(prefix?: string): string {
  // Use React's built-in useId if available (React 18+)
  const reactUseId = (React as any).useId;
  const reactId = reactUseId ? reactUseId() : null;

  const idRef = useRef<string>();

  if (!idRef.current) {
    if (reactId) {
      idRef.current = prefix ? `${prefix}-${reactId}` : reactId;
    } else {
      idRef.current = generateId(prefix);
    }
  }

  return idRef.current || '';
}

// Hook for keyboard navigation
export function useKeyboardNavigation(
  handlers: Partial<Record<keyof typeof KeyboardKeys, () => void>>,
  deps: React.DependencyList = []
) {
  const handleKeyDown = useCallback((event: KeyboardEvent) => {
    handleKeyboardNavigation(event, handlers);
  }, deps);

  return handleKeyDown;
}

// Hook for managing ARIA attributes
export function useAriaAttributes() {
  const [attributes, setAttributes] = useState<Record<string, string>>({});

  const setAriaAttribute = useCallback((key: string, value: string | boolean | number) => {
    setAttributes(prev => ({
      ...prev,
      [`aria-${key}`]: String(value)
    }));
  }, []);

  const removeAriaAttribute = useCallback((key: string) => {
    setAttributes(prev => {
      const newAttributes = { ...prev };
      delete newAttributes[`aria-${key}`];
      return newAttributes;
    });
  }, []);

  const setExpanded = useCallback((expanded: boolean) => {
    setAriaAttribute('expanded', expanded);
  }, [setAriaAttribute]);

  const setSelected = useCallback((selected: boolean) => {
    setAriaAttribute('selected', selected);
  }, [setAriaAttribute]);

  const setPressed = useCallback((pressed: boolean) => {
    setAriaAttribute('pressed', pressed);
  }, [setAriaAttribute]);

  const setChecked = useCallback((checked: boolean | 'mixed') => {
    setAriaAttribute('checked', checked);
  }, [setAriaAttribute]);

  const setDisabled = useCallback((disabled: boolean) => {
    setAriaAttribute('disabled', disabled);
  }, [setAriaAttribute]);

  const setHidden = useCallback((hidden: boolean) => {
    setAriaAttribute('hidden', hidden);
  }, [setAriaAttribute]);

  const setLabel = useCallback((label: string) => {
    setAriaAttribute('label', label);
  }, [setAriaAttribute]);

  const setLabelledBy = useCallback((id: string) => {
    setAriaAttribute('labelledby', id);
  }, [setAriaAttribute]);

  const setDescribedBy = useCallback((id: string) => {
    setAriaAttribute('describedby', id);
  }, [setAriaAttribute]);

  return {
    attributes,
    setAriaAttribute,
    removeAriaAttribute,
    setExpanded,
    setSelected,
    setPressed,
    setChecked,
    setDisabled,
    setHidden,
    setLabel,
    setLabelledBy,
    setDescribedBy,
  };
}

// Hook for screen reader announcements
export function useAnnouncer() {
  const announce = useCallback((
    message: string, 
    priority: 'polite' | 'assertive' = 'polite'
  ) => {
    announceToScreenReader(message, priority);
  }, []);

  const announcePolite = useCallback((message: string) => {
    announce(message, 'polite');
  }, [announce]);

  const announceAssertive = useCallback((message: string) => {
    announce(message, 'assertive');
  }, [announce]);

  return {
    announce,
    announcePolite,
    announceAssertive,
  };
}

// Hook for managing roving tabindex
export function useRovingTabIndex(items: HTMLElement[], activeIndex: number = 0) {
  useEffect(() => {
    items.forEach((item, index) => {
      if (item) {
        item.tabIndex = index === activeIndex ? 0 : -1;
      }
    });
  }, [items, activeIndex]);

  const moveToNext = useCallback(() => {
    const nextIndex = (activeIndex + 1) % items.length;
    const nextItem = items[nextIndex];
    if (nextItem) {
      nextItem.focus();
    }
    return nextIndex;
  }, [items, activeIndex]);

  const moveToPrevious = useCallback(() => {
    const prevIndex = activeIndex === 0 ? items.length - 1 : activeIndex - 1;
    const prevItem = items[prevIndex];
    if (prevItem) {
      prevItem.focus();
    }
    return prevIndex;
  }, [items, activeIndex]);

  const moveToFirst = useCallback(() => {
    const firstItem = items[0];
    if (firstItem) {
      firstItem.focus();
    }
    return 0;
  }, [items]);

  const moveToLast = useCallback(() => {
    const lastIndex = items.length - 1;
    const lastItem = items[lastIndex];
    if (lastItem) {
      lastItem.focus();
    }
    return lastIndex;
  }, [items]);

  return {
    moveToNext,
    moveToPrevious,
    moveToFirst,
    moveToLast,
  };
}

// Hook for detecting user preferences
export function useUserPreferences() {
  const [preferences, setPreferences] = useState({
    reducedMotion: false,
    highContrast: false,
  });

  useEffect(() => {
    const updatePreferences = () => {
      setPreferences({
        reducedMotion: prefersReducedMotion(),
        highContrast: prefersHighContrast(),
      });
    };

    updatePreferences();

    const reducedMotionQuery = window.matchMedia('(prefers-reduced-motion: reduce)');
    const highContrastQuery = window.matchMedia('(prefers-contrast: high)');

    reducedMotionQuery.addEventListener('change', updatePreferences);
    highContrastQuery.addEventListener('change', updatePreferences);

    return () => {
      reducedMotionQuery.removeEventListener('change', updatePreferences);
      highContrastQuery.removeEventListener('change', updatePreferences);
    };
  }, []);

  return preferences;
}

// Hook for managing disclosure state (collapsible content)
export function useDisclosure(defaultOpen: boolean = false) {
  const [isOpen, setIsOpen] = useState(defaultOpen);
  const triggerId = useId('disclosure-trigger');
  const contentId = useId('disclosure-content');

  const toggle = useCallback(() => {
    setIsOpen(prev => !prev);
  }, []);

  const open = useCallback(() => {
    setIsOpen(true);
  }, []);

  const close = useCallback(() => {
    setIsOpen(false);
  }, []);

  const triggerProps = {
    id: triggerId,
    'aria-expanded': isOpen,
    'aria-controls': contentId,
    onClick: toggle,
  };

  const contentProps = {
    id: contentId,
    'aria-labelledby': triggerId,
    hidden: !isOpen,
  };

  return {
    isOpen,
    toggle,
    open,
    close,
    triggerProps,
    contentProps,
  };
}

// Hook for managing listbox/combobox state
export function useListbox<T>(
  items: T[],
  onSelectionChange?: (item: T, index: number) => void
) {
  const [activeIndex, setActiveIndex] = useState(0);
  const [selectedIndex, setSelectedIndex] = useState<number | null>(null);
  const listboxId = useId('listbox');
  const activeDescendantId = useId('option');

  const selectItem = useCallback((index: number) => {
    setSelectedIndex(index);
    onSelectionChange?.(items[index], index);
  }, [items, onSelectionChange]);

  const moveToNext = useCallback(() => {
    setActiveIndex(prev => (prev + 1) % items.length);
  }, [items.length]);

  const moveToPrevious = useCallback(() => {
    setActiveIndex(prev => prev === 0 ? items.length - 1 : prev - 1);
  }, [items.length]);

  const moveToFirst = useCallback(() => {
    setActiveIndex(0);
  }, []);

  const moveToLast = useCallback(() => {
    setActiveIndex(items.length - 1);
  }, [items.length]);

  const selectActive = useCallback(() => {
    selectItem(activeIndex);
  }, [activeIndex, selectItem]);

  const listboxProps = {
    id: listboxId,
    role: 'listbox',
    'aria-activedescendant': `${activeDescendantId}-${activeIndex}`,
  };

  const getOptionProps = (index: number) => ({
    id: `${activeDescendantId}-${index}`,
    role: 'option',
    'aria-selected': selectedIndex === index,
    'data-active': activeIndex === index,
  });

  return {
    activeIndex,
    selectedIndex,
    selectItem,
    moveToNext,
    moveToPrevious,
    moveToFirst,
    moveToLast,
    selectActive,
    listboxProps,
    getOptionProps,
  };
}

// Hook for managing skip links
export function useSkipLinks() {
  const skipLinksRef = useRef<HTMLDivElement>(null);

  const addSkipLink = useCallback((targetId: string, text: string) => {
    if (!skipLinksRef.current) return;

    const link = document.createElement('a');
    link.href = `#${targetId}`;
    link.textContent = text;
    link.className = 'sr-only focus:not-sr-only focus:absolute focus:top-0 focus:left-0 bg-primary text-primary-foreground p-2 z-50 rounded';
    
    skipLinksRef.current.appendChild(link);

    return () => {
      if (link.parentNode) {
        link.parentNode.removeChild(link);
      }
    };
  }, []);

  return {
    skipLinksRef,
    addSkipLink,
  };
}
