import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { prisma } from "@/lib/database";
import { z } from "zod";

const trackingEventSchema = z.object({
  action: z.enum([
    "tutorial_started",
    "tutorial_step_viewed",
    "tutorial_step_completed",
    "tutorial_step_error",
    "tutorial_step_skipped",
    "tutorial_paused",
    "tutorial_resumed",
    "tutorial_completed",
    "tutorial_abandoned",
    "tutorial_feedback",
    "tutorial_session_end",
    "tutorial_hint_used",
    "tutorial_replay_step",
  ]),
  metadata: z.object({
    stepIndex: z.number().optional(),
    stepId: z.string().optional(),
    timeSpent: z.number().optional(),
    errorMessage: z.string().optional(),
    errorType: z.string().optional(),
    rating: z.number().min(1).max(5).optional(),
    feedback: z.string().optional(),
    device: z.string().optional(),
    browser: z.string().optional(),
    screenResolution: z.string().optional(),
    country: z.string().optional(),
    region: z.string().optional(),
    sessionId: z.string().optional(),
    userAgent: z.string().optional(),
    referrer: z.string().optional(),
    stepsCompleted: z.number().optional(),
    hintsUsed: z.number().optional(),
    replaysUsed: z.number().optional(),
    customData: z.record(z.any()).optional(),
  }).optional(),
});

// POST /api/tutorials/[id]/analytics/track - Track tutorial events
export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession();

    if (!session?.user?.email) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const user = await prisma.user.findUnique({
      where: { email: session.user.email },
    });

    if (!user) {
      return NextResponse.json({ error: "User not found" }, { status: 404 });
    }

    const tutorialId = params.id;
    const body = await request.json();

    // Validate request body
    const validatedData = trackingEventSchema.parse(body);

    // Check if tutorial exists
    const tutorial = await prisma.tutorial.findUnique({
      where: { id: tutorialId },
    });

    if (!tutorial) {
      return NextResponse.json({ error: "Tutorial not found" }, { status: 404 });
    }

    // Get client IP and user agent for additional context
    const clientIP = request.headers.get("x-forwarded-for") || 
                    request.headers.get("x-real-ip") || 
                    "unknown";
    const userAgent = request.headers.get("user-agent") || "unknown";

    // Enhance metadata with request context
    const enhancedMetadata = {
      ...validatedData.metadata,
      userAgent: validatedData.metadata?.userAgent || userAgent,
      clientIP,
      timestamp: new Date().toISOString(),
    };

    // Create analytics record
    const analyticsRecord = await prisma.analytics.create({
      data: {
        userId: user.id,
        tutorialId,
        action: validatedData.action,
        metadata: enhancedMetadata,
      },
    });

    // Handle specific actions that require additional processing
    switch (validatedData.action) {
      case "tutorial_started":
        await handleTutorialStarted(tutorialId, user.id, enhancedMetadata);
        break;
      
      case "tutorial_completed":
        await handleTutorialCompleted(tutorialId, user.id, enhancedMetadata);
        break;
      
      case "tutorial_step_completed":
        await handleStepCompleted(tutorialId, user.id, enhancedMetadata);
        break;
      
      case "tutorial_step_error":
        await handleStepError(tutorialId, user.id, enhancedMetadata);
        break;
      
      case "tutorial_feedback":
        await handleFeedback(tutorialId, user.id, enhancedMetadata);
        break;
      
      case "tutorial_abandoned":
        await handleTutorialAbandoned(tutorialId, user.id, enhancedMetadata);
        break;
    }

    return NextResponse.json({
      success: true,
      eventId: analyticsRecord.id,
      timestamp: analyticsRecord.timestamp,
    });
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Invalid request data", details: error.errors },
        { status: 400 }
      );
    }

    console.error("Error tracking tutorial event:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

async function handleTutorialStarted(tutorialId: string, userId: string, metadata: any) {
  // Update or create tutorial progress
  await prisma.tutorialProgress.upsert({
    where: {
      tutorialId_userId: {
        tutorialId,
        userId,
      },
    },
    update: {
      startedAt: new Date(),
      currentStep: 0,
      progress: 0,
    },
    create: {
      tutorialId,
      userId,
      currentStep: 0,
      progress: 0,
      startedAt: new Date(),
    },
  });

  // Update tutorial view count
  await prisma.$executeRaw`
    UPDATE "Tutorial"
    SET "metadata" = jsonb_set(
      "metadata", 
      '{analytics,views}', 
      (COALESCE(("metadata"->'analytics'->>'views')::int, 0) + 1)::text::jsonb
    )
    WHERE "id" = ${tutorialId}
  `;
}

async function handleTutorialCompleted(tutorialId: string, userId: string, metadata: any) {
  const timeSpent = metadata.timeSpent || 0;
  
  // Update tutorial progress
  await prisma.tutorialProgress.upsert({
    where: {
      tutorialId_userId: {
        tutorialId,
        userId,
      },
    },
    update: {
      completed: true,
      completedAt: new Date(),
      progress: 100,
      timeSpent,
    },
    create: {
      tutorialId,
      userId,
      completed: true,
      completedAt: new Date(),
      progress: 100,
      timeSpent,
      startedAt: new Date(),
    },
  });

  // Update tutorial completion count
  await prisma.$executeRaw`
    UPDATE "Tutorial"
    SET "metadata" = jsonb_set(
      "metadata", 
      '{analytics,completions}', 
      (COALESCE(("metadata"->'analytics'->>'completions')::int, 0) + 1)::text::jsonb
    )
    WHERE "id" = ${tutorialId}
  `;
}

async function handleStepCompleted(tutorialId: string, userId: string, metadata: any) {
  const stepIndex = metadata.stepIndex;
  if (stepIndex !== undefined) {
    // Update user progress
    await prisma.tutorialProgress.updateMany({
      where: {
        tutorialId,
        userId,
      },
      data: {
        currentStep: stepIndex + 1,
        progress: Math.min(((stepIndex + 1) / (metadata.totalSteps || stepIndex + 1)) * 100, 100),
      },
    });
  }
}

async function handleStepError(tutorialId: string, userId: string, metadata: any) {
  // Log error for analysis
  console.log(`Step error in tutorial ${tutorialId}:`, {
    userId,
    stepIndex: metadata.stepIndex,
    errorType: metadata.errorType,
    errorMessage: metadata.errorMessage,
  });
}

async function handleFeedback(tutorialId: string, userId: string, metadata: any) {
  if (metadata.rating) {
    // Update tutorial average rating
    const currentRatings = await prisma.$queryRaw`
      SELECT 
        AVG(CAST(metadata->>'rating' AS FLOAT)) as avg_rating,
        COUNT(*) as count
      FROM "Analytics"
      WHERE "tutorialId" = ${tutorialId}
        AND "action" = 'tutorial_feedback'
        AND metadata->>'rating' IS NOT NULL
    `;

    const avgRating = currentRatings[0]?.avg_rating || metadata.rating;
    
    await prisma.$executeRaw`
      UPDATE "Tutorial"
      SET "metadata" = jsonb_set(
        "metadata", 
        '{analytics,averageRating}', 
        ${avgRating}::text::jsonb
      )
      WHERE "id" = ${tutorialId}
    `;
  }
}

async function handleTutorialAbandoned(tutorialId: string, userId: string, metadata: any) {
  const timeSpent = metadata.timeSpent || 0;
  const currentStep = metadata.stepIndex || 0;
  
  // Update progress with abandonment info
  await prisma.tutorialProgress.updateMany({
    where: {
      tutorialId,
      userId,
      completed: false,
    },
    data: {
      timeSpent,
      currentStep,
    },
  });
}
