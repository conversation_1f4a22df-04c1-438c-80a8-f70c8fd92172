export interface Tutorial {
  id: string;
  title: string;
  description?: string;
  steps: TutorialStep[];
  language: string;
  isActive: boolean;
  metadata: TutorialMetadata;
  createdBy: string;
  createdAt: Date;
  updatedAt: Date;
  // Enhanced fields for versioning and collaboration
  parentId?: string; // For version history
  version: string;
  status: TutorialStatus;
  publishedAt?: Date;
  lastEditedBy?: string;
  collaborators?: TutorialCollaborator[];
  tags?: string[];
  thumbnail?: string;
  estimatedDuration?: number;
}

export interface TutorialStep {
  id: string;
  title: string;
  description: string;
  selector?: string;
  action: "click" | "hover" | "type" | "scroll" | "wait" | "explain" | "navigate" | "form_fill";
  content: string;
  voiceContent?: string;
  position: {
    x: number;
    y: number;
  };
  duration?: number;
  conditions?: {
    url?: string;
    element?: string;
    text?: string;
  };
  // Enhanced step properties
  order: number;
  isOptional?: boolean;
  hints?: string[];
  validation?: StepValidation;
  media?: StepMedia;
  interactionType?: "automatic" | "manual" | "guided";
  nextStepConditions?: NextStepCondition[];
}

export interface StepValidation {
  type: "element_exists" | "text_contains" | "url_matches" | "custom";
  value: string;
  errorMessage?: string;
}

export interface StepMedia {
  type: "image" | "video" | "audio";
  url: string;
  alt?: string;
  caption?: string;
}

export interface NextStepCondition {
  type: "success" | "failure" | "timeout";
  nextStepId?: string;
  action?: "retry" | "skip" | "end";
}

export interface TutorialMetadata {
  category: string;
  difficulty: "beginner" | "intermediate" | "advanced";
  estimatedTime: number;
  tags: string[];
  targetUrl?: string;
  version: string;
  analytics: {
    views: number;
    completions: number;
    averageRating: number;
    feedback: TutorialFeedback[];
    // Enhanced analytics
    dropOffPoints: DropOffPoint[];
    averageCompletionTime: number;
    successRate: number;
    popularSteps: number[];
    errorPoints: ErrorPoint[];
  };
  // Enhanced metadata
  prerequisites?: string[];
  learningObjectives?: string[];
  targetAudience?: string;
  lastReviewed?: Date;
  reviewedBy?: string;
  changeLog?: VersionChange[];
  accessibility?: AccessibilityFeatures;
}

export interface TutorialFeedback {
  userId: string;
  rating: number;
  comment?: string;
  timestamp: Date;
}

export interface CreateTutorialData {
  title: string;
  description?: string;
  steps: Omit<TutorialStep, "id">[];
  language?: string;
  metadata: Omit<TutorialMetadata, "analytics">;
}

export interface UpdateTutorialData {
  title?: string;
  description?: string;
  steps?: TutorialStep[];
  language?: string;
  isActive?: boolean;
  metadata?: Partial<TutorialMetadata>;
  status?: TutorialStatus;
  collaborators?: TutorialCollaborator[];
  tags?: string[];
  thumbnail?: string;
}

// New interfaces for enhanced functionality
export interface TutorialStatus {
  value: "draft" | "review" | "published" | "archived" | "deprecated";
  changedAt: Date;
  changedBy: string;
  reason?: string;
}

export interface TutorialCollaborator {
  userId: string;
  role: "owner" | "editor" | "reviewer" | "viewer";
  permissions: CollaboratorPermission[];
  addedAt: Date;
  addedBy: string;
}

export interface CollaboratorPermission {
  action: "read" | "write" | "delete" | "publish" | "share" | "manage_collaborators";
  granted: boolean;
}

export interface DropOffPoint {
  stepId: string;
  dropOffRate: number;
  commonReasons: string[];
}

export interface ErrorPoint {
  stepId: string;
  errorType: string;
  frequency: number;
  errorMessage: string;
}

export interface VersionChange {
  version: string;
  date: Date;
  author: string;
  changes: string[];
  type: "major" | "minor" | "patch";
}

export interface AccessibilityFeatures {
  screenReaderSupport: boolean;
  keyboardNavigation: boolean;
  highContrast: boolean;
  audioDescriptions: boolean;
  closedCaptions: boolean;
}

export interface TutorialVersion {
  id: string;
  tutorialId: string;
  version: string;
  title: string;
  description?: string;
  steps: TutorialStep[];
  metadata: TutorialMetadata;
  createdBy: string;
  createdAt: Date;
  isActive: boolean;
  changeNotes?: string;
}

export interface TutorialTemplate {
  id: string;
  name: string;
  description: string;
  category: string;
  steps: Omit<TutorialStep, "id">[];
  metadata: Partial<TutorialMetadata>;
  isPublic: boolean;
  createdBy: string;
  createdAt: Date;
  usageCount: number;
}

export interface TutorialProgress {
  tutorialId: string;
  userId: string;
  currentStep: number;
  completed: boolean;
  startedAt: Date;
  completedAt?: Date;
  timeSpent: number;
}
