import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { prisma } from "@/lib/database";
import { hasPermission } from "@/lib/permissions";

// POST /api/tutorials/[id]/versions/[versionId]/rollback - Rollback to specific version
export async function POST(
  request: NextRequest,
  { params }: { params: { id: string; versionId: string } }
) {
  try {
    const session = await getServerSession();

    if (!session?.user?.email) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const user = await prisma.user.findUnique({
      where: { email: session.user.email },
    });

    if (!user) {
      return NextResponse.json({ error: "User not found" }, { status: 404 });
    }

    const tutorialId = params.id;
    const versionId = params.versionId;
    const body = await request.json();
    const { reason } = body;

    // Check if tutorial exists and user has permission
    const tutorial = await prisma.tutorial.findUnique({
      where: { id: tutorialId },
      include: {
        collaborators: true,
      },
    });

    if (!tutorial) {
      return NextResponse.json({ error: "Tutorial not found" }, { status: 404 });
    }

    // Check permissions
    const canWrite = 
      tutorial.createdBy === user.id ||
      tutorial.collaborators.some(c => c.userId === user.id && 
        JSON.parse(c.permissions as string).some((p: any) => p.action === "write" && p.granted)) ||
      await hasPermission(user, "TUTORIAL_WRITE");

    if (!canWrite) {
      return NextResponse.json({ error: "Forbidden" }, { status: 403 });
    }

    // Get the version to rollback to
    const targetVersion = await prisma.tutorialVersion.findUnique({
      where: { id: versionId },
      include: {
        creator: {
          select: {
            id: true,
            name: true,
            email: true,
            image: true,
          },
        },
      },
    });

    if (!targetVersion || targetVersion.tutorialId !== tutorialId) {
      return NextResponse.json({ error: "Version not found" }, { status: 404 });
    }

    // Create a backup of current version before rollback
    await prisma.tutorialVersion.create({
      data: {
        tutorialId,
        version: tutorial.version,
        title: tutorial.title,
        description: tutorial.description,
        steps: tutorial.steps,
        metadata: tutorial.metadata,
        createdBy: user.id,
        changeNotes: `Backup before rollback to version ${targetVersion.version}`,
      },
    });

    // Generate new version number for rollback
    const versionParts = tutorial.version.split('.').map(Number);
    versionParts[1] += 1; // Increment minor version for rollback
    versionParts[2] = 0;   // Reset patch version
    const newVersionNumber = versionParts.join('.');

    // Rollback tutorial to target version
    const updatedTutorial = await prisma.tutorial.update({
      where: { id: tutorialId },
      data: {
        title: targetVersion.title,
        description: targetVersion.description,
        steps: targetVersion.steps,
        metadata: targetVersion.metadata,
        version: newVersionNumber,
        lastEditedBy: user.id,
        updatedAt: new Date(),
      },
      include: {
        creator: {
          select: {
            id: true,
            name: true,
            email: true,
            image: true,
          },
        },
        lastEditor: {
          select: {
            id: true,
            name: true,
            email: true,
            image: true,
          },
        },
      },
    });

    // Create version record for the rollback
    await prisma.tutorialVersion.create({
      data: {
        tutorialId,
        version: newVersionNumber,
        title: targetVersion.title,
        description: targetVersion.description,
        steps: targetVersion.steps,
        metadata: targetVersion.metadata,
        createdBy: user.id,
        changeNotes: `Rolled back to version ${targetVersion.version}${reason ? `: ${reason}` : ''}`,
      },
    });

    // Log analytics
    await prisma.analytics.create({
      data: {
        userId: user.id,
        tutorialId,
        action: "tutorial_rollback",
        metadata: {
          fromVersion: tutorial.version,
          toVersion: targetVersion.version,
          newVersion: newVersionNumber,
          reason: reason || "No reason provided",
          targetVersionId: versionId,
        },
      },
    });

    // Log audit trail
    await prisma.auditLog.create({
      data: {
        userId: user.id,
        action: "tutorial_rollback",
        resource: "tutorial",
        resourceId: tutorialId,
        oldValues: {
          version: tutorial.version,
          title: tutorial.title,
          description: tutorial.description,
        },
        newValues: {
          version: newVersionNumber,
          title: targetVersion.title,
          description: targetVersion.description,
        },
        metadata: {
          targetVersion: targetVersion.version,
          reason,
        },
      },
    });

    return NextResponse.json({
      message: "Tutorial rolled back successfully",
      tutorial: updatedTutorial,
      rolledBackFrom: tutorial.version,
      rolledBackTo: targetVersion.version,
      newVersion: newVersionNumber,
    });
  } catch (error) {
    console.error("Error rolling back tutorial:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
