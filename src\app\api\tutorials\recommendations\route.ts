import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { prisma } from "@/lib/database";

// GET /api/tutorials/recommendations - Get personalized tutorial recommendations
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession();

    if (!session?.user?.email) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const user = await prisma.user.findUnique({
      where: { email: session.user.email },
      include: {
        learningProfile: true,
        tutorialProgress: {
          include: {
            tutorial: {
              select: {
                id: true,
                metadata: true,
                tags: true,
              },
            },
          },
        },
      },
    });

    if (!user) {
      return NextResponse.json({ error: "User not found" }, { status: 404 });
    }

    const { searchParams } = new URL(request.url);
    const limit = parseInt(searchParams.get("limit") || "10");
    const type = searchParams.get("type") || "all"; // all, similar, trending, personalized

    let recommendations: any[] = [];

    switch (type) {
      case "similar":
        recommendations = await getSimilarTutorials(user, limit);
        break;
      case "trending":
        recommendations = await getTrendingTutorials(user, limit);
        break;
      case "personalized":
        recommendations = await getPersonalizedRecommendations(user, limit);
        break;
      case "all":
      default:
        const [similar, trending, personalized] = await Promise.all([
          getSimilarTutorials(user, Math.ceil(limit / 3)),
          getTrendingTutorials(user, Math.ceil(limit / 3)),
          getPersonalizedRecommendations(user, Math.ceil(limit / 3)),
        ]);
        recommendations = [
          ...personalized.map(t => ({ ...t, recommendationType: "personalized" })),
          ...similar.map(t => ({ ...t, recommendationType: "similar" })),
          ...trending.map(t => ({ ...t, recommendationType: "trending" })),
        ];
        break;
    }

    // Remove duplicates and limit results
    const uniqueRecommendations = recommendations
      .filter((tutorial, index, self) => 
        index === self.findIndex(t => t.id === tutorial.id)
      )
      .slice(0, limit);

    // Log recommendation view
    await prisma.analytics.create({
      data: {
        userId: user.id,
        action: "recommendations_viewed",
        metadata: {
          type,
          count: uniqueRecommendations.length,
          recommendationTypes: [...new Set(uniqueRecommendations.map(r => r.recommendationType))],
        },
      },
    });

    return NextResponse.json({
      recommendations: uniqueRecommendations,
      total: uniqueRecommendations.length,
      type,
    });
  } catch (error) {
    console.error("Error fetching recommendations:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

async function getSimilarTutorials(user: any, limit: number) {
  // Get user's completed tutorials
  const completedTutorials = user.tutorialProgress
    .filter((p: any) => p.completed)
    .map((p: any) => p.tutorial);

  if (completedTutorials.length === 0) {
    return [];
  }

  // Extract categories and tags from completed tutorials
  const userCategories = completedTutorials.map((t: any) => (t.metadata as any)?.category).filter(Boolean);
  const userTags = completedTutorials.flatMap((t: any) => t.tags || []);

  // Find similar tutorials
  const similarTutorials = await prisma.tutorial.findMany({
    where: {
      isActive: true,
      status: "published",
      id: {
        notIn: completedTutorials.map((t: any) => t.id),
      },
      OR: [
        {
          metadata: {
            path: ["category"],
            in: userCategories,
          },
        },
        {
          tags: {
            hasSome: userTags,
          },
        },
      ],
    },
    include: {
      creator: {
        select: {
          id: true,
          name: true,
          email: true,
          image: true,
        },
      },
      _count: {
        select: {
          progress: {
            where: { completed: true },
          },
        },
      },
    },
    take: limit,
    orderBy: {
      createdAt: "desc",
    },
  });

  return similarTutorials.map(tutorial => ({
    ...tutorial,
    similarityScore: calculateSimilarityScore(tutorial, userCategories, userTags),
  }));
}

async function getTrendingTutorials(user: any, limit: number) {
  // Get tutorials with high recent activity
  const oneWeekAgo = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);

  const trendingTutorials = await prisma.tutorial.findMany({
    where: {
      isActive: true,
      status: "published",
      createdAt: { gte: oneWeekAgo },
    },
    include: {
      creator: {
        select: {
          id: true,
          name: true,
          email: true,
          image: true,
        },
      },
      _count: {
        select: {
          analytics: {
            where: {
              action: "tutorial_viewed",
              timestamp: { gte: oneWeekAgo },
            },
          },
          progress: {
            where: {
              startedAt: { gte: oneWeekAgo },
            },
          },
        },
      },
    },
    take: limit * 2, // Get more to filter and sort
  });

  // Calculate trending score and sort
  const scoredTutorials = trendingTutorials
    .map(tutorial => ({
      ...tutorial,
      trendingScore: tutorial._count.analytics * 2 + tutorial._count.progress * 5,
    }))
    .sort((a, b) => b.trendingScore - a.trendingScore)
    .slice(0, limit);

  return scoredTutorials;
}

async function getPersonalizedRecommendations(user: any, limit: number) {
  const profile = user.learningProfile;
  if (!profile) {
    return [];
  }

  // Build personalized query based on learning profile
  const whereClause: any = {
    isActive: true,
    status: "published",
    id: {
      notIn: user.tutorialProgress.map((p: any) => p.tutorialId),
    },
  };

  // Prefer user's preferred categories
  if (profile.preferredCategories.length > 0) {
    whereClause.OR = [
      {
        metadata: {
          path: ["category"],
          in: profile.preferredCategories,
        },
      },
    ];
  }

  // Match difficulty level
  if (profile.difficultyLevel) {
    whereClause.metadata = {
      ...whereClause.metadata,
      path: ["difficulty"],
      equals: profile.difficultyLevel,
    };
  }

  // Match estimated duration to user's session preference
  if (profile.sessionDuration) {
    const maxDuration = Math.ceil(profile.sessionDuration / 60); // Convert to minutes
    whereClause.estimatedDuration = {
      lte: maxDuration,
    };
  }

  // Include weak topics for learning opportunities
  if (profile.weakTopics.length > 0) {
    whereClause.OR = [
      ...(whereClause.OR || []),
      {
        tags: {
          hasSome: profile.weakTopics,
        },
      },
      {
        metadata: {
          path: ["category"],
          in: profile.weakTopics,
        },
      },
    ];
  }

  const personalizedTutorials = await prisma.tutorial.findMany({
    where: whereClause,
    include: {
      creator: {
        select: {
          id: true,
          name: true,
          email: true,
          image: true,
        },
      },
      _count: {
        select: {
          progress: {
            where: { completed: true },
          },
        },
      },
    },
    take: limit,
    orderBy: [
      { updatedAt: "desc" },
      { createdAt: "desc" },
    ],
  });

  return personalizedTutorials.map(tutorial => ({
    ...tutorial,
    personalizationScore: calculatePersonalizationScore(tutorial, profile),
  }));
}

function calculateSimilarityScore(tutorial: any, userCategories: string[], userTags: string[]): number {
  let score = 0;
  
  const tutorialCategory = (tutorial.metadata as any)?.category;
  const tutorialTags = tutorial.tags || [];

  // Category match
  if (tutorialCategory && userCategories.includes(tutorialCategory)) {
    score += 10;
  }

  // Tag matches
  const tagMatches = tutorialTags.filter((tag: string) => userTags.includes(tag)).length;
  score += tagMatches * 3;

  // Popularity boost
  score += tutorial._count.progress * 0.5;

  return score;
}

function calculatePersonalizationScore(tutorial: any, profile: any): number {
  let score = 0;
  
  const tutorialMetadata = tutorial.metadata as any;
  const tutorialTags = tutorial.tags || [];

  // Preferred category match
  if (profile.preferredCategories.includes(tutorialMetadata.category)) {
    score += 15;
  }

  // Difficulty match
  if (profile.difficultyLevel === tutorialMetadata.difficulty) {
    score += 10;
  }

  // Duration preference
  const estimatedTime = tutorialMetadata.estimatedTime || 0;
  if (estimatedTime <= profile.sessionDuration / 60) {
    score += 5;
  }

  // Strong topics
  const strongTopicMatches = profile.strongTopics.filter((topic: string) => 
    tutorialTags.includes(topic) || tutorialMetadata.category === topic
  ).length;
  score += strongTopicMatches * 7;

  // Weak topics (learning opportunities)
  const weakTopicMatches = profile.weakTopics.filter((topic: string) => 
    tutorialTags.includes(topic) || tutorialMetadata.category === topic
  ).length;
  score += weakTopicMatches * 20; // Higher weight for learning opportunities

  // Learning style preferences
  if (profile.learningStyle === "visual" && tutorialMetadata.hasVisualContent) {
    score += 3;
  }
  if (profile.learningStyle === "auditory" && tutorialMetadata.hasAudioContent) {
    score += 3;
  }

  return score;
}
