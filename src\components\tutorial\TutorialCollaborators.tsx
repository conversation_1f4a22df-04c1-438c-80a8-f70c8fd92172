"use client";

import React, { useState, useEffect } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import { useToast } from "@/components/ui/use-toast";
import {
  Users,
  Plus,
  MoreHorizontal,
  Crown,
  Edit,
  Trash,
  Mail,
  Shield,
  Eye,
  Pencil,
  Share,
  Settings,
} from "lucide-react";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { formatDistanceToNow } from "date-fns";
import { Tutorial } from "@/models/Tutorial";

interface Collaborator {
  id: string;
  user: {
    id: string;
    name: string;
    email: string;
    image?: string;
  };
  role: "owner" | "editor" | "reviewer" | "viewer";
  permissions: Array<{
    action: string;
    granted: boolean;
  }>;
  addedAt: string;
  addedBy?: {
    id: string;
    name: string;
    email: string;
  };
  isOwner: boolean;
}

interface TutorialCollaboratorsProps {
  tutorial: Tutorial;
  onUpdate: () => void;
}

const addCollaboratorSchema = z.object({
  email: z.string().email("Please enter a valid email address"),
  role: z.enum(["editor", "reviewer", "viewer"]).default("viewer"),
  permissions: z.object({
    read: z.boolean().default(true),
    write: z.boolean().default(false),
    delete: z.boolean().default(false),
    publish: z.boolean().default(false),
    share: z.boolean().default(false),
    manage_collaborators: z.boolean().default(false),
  }),
  message: z.string().optional(),
});

type AddCollaboratorFormValues = z.infer<typeof addCollaboratorSchema>;

export default function TutorialCollaborators({ tutorial, onUpdate }: TutorialCollaboratorsProps) {
  const { toast } = useToast();
  const [collaborators, setCollaborators] = useState<Collaborator[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isAddingCollaborator, setIsAddingCollaborator] = useState(false);
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);

  const form = useForm<AddCollaboratorFormValues>({
    resolver: zodResolver(addCollaboratorSchema),
    defaultValues: {
      role: "viewer",
      permissions: {
        read: true,
        write: false,
        delete: false,
        publish: false,
        share: false,
        manage_collaborators: false,
      },
    },
  });

  useEffect(() => {
    loadCollaborators();
  }, [tutorial.id]);

  const loadCollaborators = async () => {
    try {
      setIsLoading(true);
      const response = await fetch(`/api/tutorials/${tutorial.id}/collaborators`);
      
      if (response.ok) {
        const data = await response.json();
        setCollaborators(data.collaborators);
      } else {
        throw new Error("Failed to load collaborators");
      }
    } catch (error) {
      console.error("Error loading collaborators:", error);
      toast({
        title: "Error",
        description: "Failed to load collaborators",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const addCollaborator = async (data: AddCollaboratorFormValues) => {
    try {
      setIsAddingCollaborator(true);
      
      const permissions = Object.entries(data.permissions)
        .filter(([_, granted]) => granted)
        .map(([action]) => ({ action, granted: true }));

      const response = await fetch(`/api/tutorials/${tutorial.id}/collaborators`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          email: data.email,
          role: data.role,
          permissions,
          message: data.message,
        }),
      });

      if (response.ok) {
        toast({
          title: "Success",
          description: "Collaborator added successfully",
        });
        
        form.reset();
        setIsAddDialogOpen(false);
        await loadCollaborators();
        onUpdate();
      } else {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to add collaborator");
      }
    } catch (error) {
      console.error("Error adding collaborator:", error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to add collaborator",
        variant: "destructive",
      });
    } finally {
      setIsAddingCollaborator(false);
    }
  };

  const removeCollaborator = async (collaboratorId: string) => {
    try {
      const response = await fetch(
        `/api/tutorials/${tutorial.id}/collaborators?collaboratorId=${collaboratorId}`,
        {
          method: "DELETE",
        }
      );

      if (response.ok) {
        toast({
          title: "Success",
          description: "Collaborator removed successfully",
        });
        
        await loadCollaborators();
        onUpdate();
      } else {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to remove collaborator");
      }
    } catch (error) {
      console.error("Error removing collaborator:", error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to remove collaborator",
        variant: "destructive",
      });
    }
  };

  const getRoleIcon = (role: string) => {
    switch (role) {
      case "owner":
        return <Crown className="h-4 w-4 text-yellow-500" />;
      case "editor":
        return <Pencil className="h-4 w-4 text-blue-500" />;
      case "reviewer":
        return <Eye className="h-4 w-4 text-green-500" />;
      case "viewer":
        return <Eye className="h-4 w-4 text-gray-500" />;
      default:
        return <Users className="h-4 w-4" />;
    }
  };

  const getPermissionIcon = (permission: string) => {
    switch (permission) {
      case "read":
        return <Eye className="h-3 w-3" />;
      case "write":
        return <Pencil className="h-3 w-3" />;
      case "delete":
        return <Trash className="h-3 w-3" />;
      case "publish":
        return <Share className="h-3 w-3" />;
      case "share":
        return <Share className="h-3 w-3" />;
      case "manage_collaborators":
        return <Settings className="h-3 w-3" />;
      default:
        return <Shield className="h-3 w-3" />;
    }
  };

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Users className="h-5 w-5" />
            Collaborators
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center h-32">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              <Users className="h-5 w-5" />
              Collaborators
            </CardTitle>
            <CardDescription>
              Manage who can access and edit this tutorial
            </CardDescription>
          </div>
          
          <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
            <DialogTrigger asChild>
              <Button className="gap-2">
                <Plus className="h-4 w-4" />
                Add Collaborator
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-md">
              <DialogHeader>
                <DialogTitle>Add Collaborator</DialogTitle>
                <DialogDescription>
                  Invite someone to collaborate on this tutorial
                </DialogDescription>
              </DialogHeader>
              
              <Form {...form}>
                <form onSubmit={form.handleSubmit(addCollaborator)} className="space-y-4">
                  <FormField
                    control={form.control}
                    name="email"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Email Address</FormLabel>
                        <FormControl>
                          <Input placeholder="<EMAIL>" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="role"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Role</FormLabel>
                        <Select onValueChange={field.onChange} defaultValue={field.value}>
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            <SelectItem value="viewer">Viewer - Can view only</SelectItem>
                            <SelectItem value="reviewer">Reviewer - Can view and comment</SelectItem>
                            <SelectItem value="editor">Editor - Can edit content</SelectItem>
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <div>
                    <FormLabel>Permissions</FormLabel>
                    <div className="grid grid-cols-2 gap-2 mt-2">
                      {[
                        { key: "read", label: "Read" },
                        { key: "write", label: "Write" },
                        { key: "share", label: "Share" },
                        { key: "manage_collaborators", label: "Manage" },
                      ].map(({ key, label }) => (
                        <FormField
                          key={key}
                          control={form.control}
                          name={`permissions.${key as keyof AddCollaboratorFormValues['permissions']}`}
                          render={({ field }) => (
                            <FormItem className="flex flex-row items-start space-x-3 space-y-0">
                              <FormControl>
                                <Checkbox
                                  checked={field.value}
                                  onCheckedChange={field.onChange}
                                />
                              </FormControl>
                              <div className="space-y-1 leading-none">
                                <FormLabel className="text-sm font-normal">
                                  {label}
                                </FormLabel>
                              </div>
                            </FormItem>
                          )}
                        />
                      ))}
                    </div>
                  </div>

                  <FormField
                    control={form.control}
                    name="message"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Message (Optional)</FormLabel>
                        <FormControl>
                          <Textarea
                            placeholder="Add a personal message..."
                            className="min-h-[60px]"
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <div className="flex justify-end gap-2">
                    <Button
                      type="button"
                      variant="outline"
                      onClick={() => setIsAddDialogOpen(false)}
                    >
                      Cancel
                    </Button>
                    <Button type="submit" disabled={isAddingCollaborator}>
                      {isAddingCollaborator ? "Adding..." : "Add Collaborator"}
                    </Button>
                  </div>
                </form>
              </Form>
            </DialogContent>
          </Dialog>
        </div>
      </CardHeader>
      
      <CardContent>
        <div className="space-y-4">
          {collaborators.map((collaborator) => (
            <div
              key={collaborator.id}
              className="flex items-center justify-between p-4 border rounded-lg"
            >
              <div className="flex items-center gap-3">
                <Avatar>
                  <AvatarImage src={collaborator.user.image} />
                  <AvatarFallback>
                    {collaborator.user.name?.charAt(0) || collaborator.user.email.charAt(0)}
                  </AvatarFallback>
                </Avatar>
                
                <div>
                  <div className="flex items-center gap-2">
                    <span className="font-medium">{collaborator.user.name || collaborator.user.email}</span>
                    {getRoleIcon(collaborator.role)}
                    <Badge variant="outline" className="text-xs">
                      {collaborator.role}
                    </Badge>
                  </div>
                  <div className="text-sm text-muted-foreground">
                    {collaborator.user.email}
                  </div>
                  <div className="flex items-center gap-1 mt-1">
                    {collaborator.permissions
                      .filter(p => p.granted)
                      .map(permission => (
                        <div
                          key={permission.action}
                          className="flex items-center gap-1 text-xs text-muted-foreground"
                          title={permission.action}
                        >
                          {getPermissionIcon(permission.action)}
                        </div>
                      ))}
                  </div>
                </div>
              </div>

              <div className="flex items-center gap-2">
                <div className="text-xs text-muted-foreground text-right">
                  <div>
                    Added {formatDistanceToNow(new Date(collaborator.addedAt), { addSuffix: true })}
                  </div>
                  {collaborator.addedBy && (
                    <div>by {collaborator.addedBy.name}</div>
                  )}
                </div>

                {!collaborator.isOwner && (
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="sm">
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem>
                        <Edit className="h-4 w-4 mr-2" />
                        Edit Permissions
                      </DropdownMenuItem>
                      <DropdownMenuItem>
                        <Mail className="h-4 w-4 mr-2" />
                        Send Message
                      </DropdownMenuItem>
                      <AlertDialog>
                        <AlertDialogTrigger asChild>
                          <DropdownMenuItem
                            className="text-destructive"
                            onSelect={(e) => e.preventDefault()}
                          >
                            <Trash className="h-4 w-4 mr-2" />
                            Remove
                          </DropdownMenuItem>
                        </AlertDialogTrigger>
                        <AlertDialogContent>
                          <AlertDialogHeader>
                            <AlertDialogTitle>Remove Collaborator</AlertDialogTitle>
                            <AlertDialogDescription>
                              Are you sure you want to remove {collaborator.user.name || collaborator.user.email} from this tutorial? 
                              They will lose access immediately.
                            </AlertDialogDescription>
                          </AlertDialogHeader>
                          <AlertDialogFooter>
                            <AlertDialogCancel>Cancel</AlertDialogCancel>
                            <AlertDialogAction
                              onClick={() => removeCollaborator(collaborator.id)}
                              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
                            >
                              Remove
                            </AlertDialogAction>
                          </AlertDialogFooter>
                        </AlertDialogContent>
                      </AlertDialog>
                    </DropdownMenuContent>
                  </DropdownMenu>
                )}
              </div>
            </div>
          ))}

          {collaborators.length === 0 && (
            <div className="text-center py-8 text-muted-foreground">
              <Users className="h-12 w-12 mx-auto mb-4 opacity-50" />
              <p>No collaborators yet</p>
              <p className="text-sm">Add collaborators to work together on this tutorial</p>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
