"use client";

import React, { useState, useEffect } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { useToast } from "@/components/ui/use-toast";
import {
  History,
  RotateCcw,
  GitBranch,
  Clock,
  User,
  Eye,
  GitCompar<PERSON>,
  Plus,
  AlertTriangle,
} from "lucide-react";
import { formatDistanceToNow } from "date-fns";

interface TutorialVersion {
  id: string;
  tutorialId: string;
  version: string;
  title: string;
  description?: string;
  createdAt: string;
  isActive: boolean;
  changeNotes?: string;
  creator: {
    id: string;
    name: string;
    email: string;
    image?: string;
  };
}

interface TutorialVersionHistoryProps {
  tutorialId: string;
  onVersionRestore?: () => void;
}

export default function TutorialVersionHistory({
  tutorialId,
  onVersionRestore,
}: TutorialVersionHistoryProps) {
  const { toast } = useToast();
  const [versions, setVersions] = useState<TutorialVersion[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isCreatingVersion, setIsCreatingVersion] = useState(false);
  const [isRollingBack, setIsRollingBack] = useState(false);
  const [selectedVersions, setSelectedVersions] = useState<{
    from: string | null;
    to: string | null;
  }>({ from: null, to: null });
  const [comparison, setComparison] = useState<any>(null);
  const [isComparing, setIsComparing] = useState(false);

  useEffect(() => {
    loadVersions();
  }, [tutorialId]);

  const loadVersions = async () => {
    try {
      setIsLoading(true);
      const response = await fetch(`/api/tutorials/${tutorialId}/versions`);
      
      if (response.ok) {
        const data = await response.json();
        setVersions(data.versions);
      } else {
        throw new Error("Failed to load versions");
      }
    } catch (error) {
      console.error("Error loading versions:", error);
      toast({
        title: "Error",
        description: "Failed to load version history",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const createVersion = async (changeNotes: string) => {
    try {
      setIsCreatingVersion(true);
      const response = await fetch(`/api/tutorials/${tutorialId}/versions`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ changeNotes }),
      });

      if (response.ok) {
        toast({
          title: "Success",
          description: "Version created successfully",
        });
        await loadVersions();
      } else {
        const error = await response.json();
        throw new Error(error.error || "Failed to create version");
      }
    } catch (error) {
      console.error("Error creating version:", error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to create version",
        variant: "destructive",
      });
    } finally {
      setIsCreatingVersion(false);
    }
  };

  const rollbackToVersion = async (versionId: string, reason: string) => {
    try {
      setIsRollingBack(true);
      const response = await fetch(
        `/api/tutorials/${tutorialId}/versions/${versionId}/rollback`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({ reason }),
        }
      );

      if (response.ok) {
        toast({
          title: "Success",
          description: "Tutorial rolled back successfully",
        });
        await loadVersions();
        onVersionRestore?.();
      } else {
        const error = await response.json();
        throw new Error(error.error || "Failed to rollback");
      }
    } catch (error) {
      console.error("Error rolling back:", error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to rollback",
        variant: "destructive",
      });
    } finally {
      setIsRollingBack(false);
    }
  };

  const compareVersions = async () => {
    if (!selectedVersions.from || !selectedVersions.to) return;

    try {
      setIsComparing(true);
      const response = await fetch(
        `/api/tutorials/${tutorialId}/versions/compare?from=${selectedVersions.from}&to=${selectedVersions.to}`
      );

      if (response.ok) {
        const data = await response.json();
        setComparison(data);
      } else {
        throw new Error("Failed to compare versions");
      }
    } catch (error) {
      console.error("Error comparing versions:", error);
      toast({
        title: "Error",
        description: "Failed to compare versions",
        variant: "destructive",
      });
    } finally {
      setIsComparing(false);
    }
  };

  const CreateVersionDialog = () => {
    const [changeNotes, setChangeNotes] = useState("");
    const [isOpen, setIsOpen] = useState(false);

    const handleCreate = async () => {
      await createVersion(changeNotes);
      setChangeNotes("");
      setIsOpen(false);
    };

    return (
      <Dialog open={isOpen} onOpenChange={setIsOpen}>
        <DialogTrigger asChild>
          <Button className="gap-2">
            <Plus className="h-4 w-4" />
            Create Version
          </Button>
        </DialogTrigger>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Create New Version</DialogTitle>
            <DialogDescription>
              Create a snapshot of the current tutorial state
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <label className="text-sm font-medium">Change Notes</label>
              <Textarea
                placeholder="Describe what changed in this version..."
                value={changeNotes}
                onChange={(e) => setChangeNotes(e.target.value)}
                className="mt-1"
              />
            </div>
            <div className="flex justify-end gap-2">
              <Button variant="outline" onClick={() => setIsOpen(false)}>
                Cancel
              </Button>
              <Button onClick={handleCreate} disabled={isCreatingVersion}>
                {isCreatingVersion ? "Creating..." : "Create Version"}
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    );
  };

  const RollbackDialog = ({ version }: { version: TutorialVersion }) => {
    const [reason, setReason] = useState("");
    const [isOpen, setIsOpen] = useState(false);

    const handleRollback = async () => {
      await rollbackToVersion(version.id, reason);
      setReason("");
      setIsOpen(false);
    };

    return (
      <AlertDialog open={isOpen} onOpenChange={setIsOpen}>
        <AlertDialogTrigger asChild>
          <Button variant="outline" size="sm" className="gap-1">
            <RotateCcw className="h-3 w-3" />
            Rollback
          </Button>
        </AlertDialogTrigger>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle className="flex items-center gap-2">
              <AlertTriangle className="h-5 w-5 text-amber-500" />
              Rollback to Version {version.version}
            </AlertDialogTitle>
            <AlertDialogDescription>
              This will revert the tutorial to version {version.version}. The current
              version will be backed up automatically. This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <div className="my-4">
            <label className="text-sm font-medium">Reason for rollback</label>
            <Textarea
              placeholder="Why are you rolling back to this version?"
              value={reason}
              onChange={(e) => setReason(e.target.value)}
              className="mt-1"
            />
          </div>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleRollback}
              disabled={isRollingBack}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              {isRollingBack ? "Rolling back..." : "Rollback"}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    );
  };

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <History className="h-5 w-5" />
            Version History
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center h-32">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <History className="h-5 w-5" />
                Version History
              </CardTitle>
              <CardDescription>
                Track changes and manage tutorial versions
              </CardDescription>
            </div>
            <div className="flex items-center gap-2">
              {selectedVersions.from && selectedVersions.to && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={compareVersions}
                  disabled={isComparing}
                  className="gap-1"
                >
                  <GitCompare className="h-4 w-4" />
                  {isComparing ? "Comparing..." : "Compare"}
                </Button>
              )}
              <CreateVersionDialog />
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {versions.map((version, index) => (
              <div
                key={version.id}
                className={`flex items-center justify-between p-4 border rounded-lg ${
                  version.isActive ? "bg-primary/5 border-primary/20" : ""
                }`}
              >
                <div className="flex items-center gap-4">
                  <div className="flex items-center gap-2">
                    <input
                      type="radio"
                      name="fromVersion"
                      value={version.id}
                      onChange={(e) =>
                        setSelectedVersions(prev => ({ ...prev, from: e.target.value }))
                      }
                      className="w-4 h-4"
                    />
                    <input
                      type="radio"
                      name="toVersion"
                      value={version.id}
                      onChange={(e) =>
                        setSelectedVersions(prev => ({ ...prev, to: e.target.value }))
                      }
                      className="w-4 h-4"
                    />
                  </div>
                  
                  <div className="flex items-center gap-2">
                    <GitBranch className="h-4 w-4 text-muted-foreground" />
                    <span className="font-mono text-sm">{version.version}</span>
                    {version.isActive && (
                      <Badge variant="default" className="text-xs">
                        Current
                      </Badge>
                    )}
                  </div>

                  <div className="flex flex-col">
                    <span className="font-medium">{version.title}</span>
                    {version.changeNotes && (
                      <span className="text-sm text-muted-foreground">
                        {version.changeNotes}
                      </span>
                    )}
                  </div>
                </div>

                <div className="flex items-center gap-4">
                  <div className="flex items-center gap-2 text-sm text-muted-foreground">
                    <User className="h-3 w-3" />
                    <span>{version.creator.name}</span>
                    <Clock className="h-3 w-3 ml-2" />
                    <span>
                      {formatDistanceToNow(new Date(version.createdAt), {
                        addSuffix: true,
                      })}
                    </span>
                  </div>

                  <div className="flex items-center gap-1">
                    <Button variant="ghost" size="sm" className="gap-1">
                      <Eye className="h-3 w-3" />
                      View
                    </Button>
                    {!version.isActive && <RollbackDialog version={version} />}
                  </div>
                </div>
              </div>
            ))}

            {versions.length === 0 && (
              <div className="text-center py-8 text-muted-foreground">
                <History className="h-12 w-12 mx-auto mb-4 opacity-50" />
                <p>No version history available</p>
                <p className="text-sm">Create your first version to start tracking changes</p>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Version Comparison Results */}
      {comparison && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <GitCompare className="h-5 w-5" />
              Version Comparison
            </CardTitle>
            <CardDescription>
              Comparing {comparison.fromVersion.version} → {comparison.toVersion.version}
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="grid grid-cols-4 gap-4 text-sm">
                <div className="text-center">
                  <div className="text-2xl font-bold text-blue-600">
                    {comparison.comparison.summary.totalChanges}
                  </div>
                  <div className="text-muted-foreground">Total Changes</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-green-600">
                    {comparison.comparison.summary.stepsAdded}
                  </div>
                  <div className="text-muted-foreground">Steps Added</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-red-600">
                    {comparison.comparison.summary.stepsRemoved}
                  </div>
                  <div className="text-muted-foreground">Steps Removed</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-amber-600">
                    {comparison.comparison.summary.stepsModified}
                  </div>
                  <div className="text-muted-foreground">Steps Modified</div>
                </div>
              </div>

              {comparison.comparison.changes.length > 0 && (
                <div>
                  <h4 className="font-medium mb-2">Basic Field Changes</h4>
                  <div className="space-y-2">
                    {comparison.comparison.changes.map((change: any, index: number) => (
                      <div key={index} className="flex items-center justify-between p-2 bg-muted rounded">
                        <span className="font-medium">{change.field}</span>
                        <div className="flex items-center gap-2 text-sm">
                          <span className="text-red-600">"{change.oldValue}"</span>
                          <span>→</span>
                          <span className="text-green-600">"{change.newValue}"</span>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              <Button
                variant="outline"
                onClick={() => setComparison(null)}
                className="w-full"
              >
                Close Comparison
              </Button>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
