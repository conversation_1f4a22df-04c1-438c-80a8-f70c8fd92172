import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { prisma } from "@/lib/database";
import { hasPermission } from "@/lib/permissions";

interface VersionComparison {
  field: string;
  oldValue: any;
  newValue: any;
  type: 'added' | 'removed' | 'modified' | 'unchanged';
}

interface StepComparison {
  stepIndex: number;
  stepId?: string;
  changes: VersionComparison[];
  type: 'added' | 'removed' | 'modified' | 'unchanged';
}

// GET /api/tutorials/[id]/versions/compare?from=version1&to=version2
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession();

    if (!session?.user?.email) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const user = await prisma.user.findUnique({
      where: { email: session.user.email },
    });

    if (!user) {
      return NextResponse.json({ error: "User not found" }, { status: 404 });
    }

    const tutorialId = params.id;
    const { searchParams } = new URL(request.url);
    const fromVersionId = searchParams.get("from");
    const toVersionId = searchParams.get("to");

    if (!fromVersionId || !toVersionId) {
      return NextResponse.json(
        { error: "Both 'from' and 'to' version IDs are required" },
        { status: 400 }
      );
    }

    // Check if tutorial exists and user has permission
    const tutorial = await prisma.tutorial.findUnique({
      where: { id: tutorialId },
      include: {
        collaborators: true,
      },
    });

    if (!tutorial) {
      return NextResponse.json({ error: "Tutorial not found" }, { status: 404 });
    }

    // Check permissions
    const canRead = 
      tutorial.createdBy === user.id ||
      tutorial.collaborators.some(c => c.userId === user.id) ||
      await hasPermission(user, "TUTORIAL_READ");

    if (!canRead) {
      return NextResponse.json({ error: "Forbidden" }, { status: 403 });
    }

    // Get versions to compare
    let fromVersion, toVersion;

    // Handle current version (use tutorial ID as version ID for current)
    if (fromVersionId === tutorialId) {
      fromVersion = {
        id: tutorial.id,
        version: tutorial.version,
        title: tutorial.title,
        description: tutorial.description,
        steps: tutorial.steps,
        metadata: tutorial.metadata,
        createdAt: tutorial.updatedAt,
      };
    } else {
      fromVersion = await prisma.tutorialVersion.findUnique({
        where: { id: fromVersionId },
      });
    }

    if (toVersionId === tutorialId) {
      toVersion = {
        id: tutorial.id,
        version: tutorial.version,
        title: tutorial.title,
        description: tutorial.description,
        steps: tutorial.steps,
        metadata: tutorial.metadata,
        createdAt: tutorial.updatedAt,
      };
    } else {
      toVersion = await prisma.tutorialVersion.findUnique({
        where: { id: toVersionId },
      });
    }

    if (!fromVersion || !toVersion) {
      return NextResponse.json({ error: "One or both versions not found" }, { status: 404 });
    }

    if (fromVersion.tutorialId !== tutorialId || toVersion.tutorialId !== tutorialId) {
      return NextResponse.json({ error: "Versions do not belong to this tutorial" }, { status: 400 });
    }

    // Compare versions
    const comparison = compareVersions(fromVersion, toVersion);

    // Log analytics
    await prisma.analytics.create({
      data: {
        userId: user.id,
        tutorialId,
        action: "tutorial_versions_compared",
        metadata: {
          fromVersion: fromVersion.version,
          toVersion: toVersion.version,
          changesCount: comparison.changes.length,
        },
      },
    });

    return NextResponse.json({
      comparison,
      fromVersion: {
        id: fromVersion.id,
        version: fromVersion.version,
        title: fromVersion.title,
        createdAt: fromVersion.createdAt,
      },
      toVersion: {
        id: toVersion.id,
        version: toVersion.version,
        title: toVersion.title,
        createdAt: toVersion.createdAt,
      },
    });
  } catch (error) {
    console.error("Error comparing tutorial versions:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

function compareVersions(fromVersion: any, toVersion: any) {
  const changes: VersionComparison[] = [];
  const stepChanges: StepComparison[] = [];

  // Compare basic fields
  const basicFields = ['title', 'description', 'language'];
  basicFields.forEach(field => {
    if (fromVersion[field] !== toVersion[field]) {
      changes.push({
        field,
        oldValue: fromVersion[field],
        newValue: toVersion[field],
        type: 'modified',
      });
    }
  });

  // Compare metadata
  const fromMetadata = typeof fromVersion.metadata === 'string' 
    ? JSON.parse(fromVersion.metadata) 
    : fromVersion.metadata;
  const toMetadata = typeof toVersion.metadata === 'string' 
    ? JSON.parse(toVersion.metadata) 
    : toVersion.metadata;

  const metadataFields = ['category', 'difficulty', 'estimatedTime', 'targetUrl'];
  metadataFields.forEach(field => {
    if (fromMetadata[field] !== toMetadata[field]) {
      changes.push({
        field: `metadata.${field}`,
        oldValue: fromMetadata[field],
        newValue: toMetadata[field],
        type: 'modified',
      });
    }
  });

  // Compare steps
  const fromSteps = typeof fromVersion.steps === 'string' 
    ? JSON.parse(fromVersion.steps) 
    : fromVersion.steps;
  const toSteps = typeof toVersion.steps === 'string' 
    ? JSON.parse(toVersion.steps) 
    : toVersion.steps;

  const maxSteps = Math.max(fromSteps.length, toSteps.length);
  
  for (let i = 0; i < maxSteps; i++) {
    const fromStep = fromSteps[i];
    const toStep = toSteps[i];
    
    if (!fromStep && toStep) {
      // Step added
      stepChanges.push({
        stepIndex: i,
        stepId: toStep.id,
        changes: [{
          field: 'step',
          oldValue: null,
          newValue: toStep,
          type: 'added',
        }],
        type: 'added',
      });
    } else if (fromStep && !toStep) {
      // Step removed
      stepChanges.push({
        stepIndex: i,
        stepId: fromStep.id,
        changes: [{
          field: 'step',
          oldValue: fromStep,
          newValue: null,
          type: 'removed',
        }],
        type: 'removed',
      });
    } else if (fromStep && toStep) {
      // Compare step fields
      const stepFieldChanges: VersionComparison[] = [];
      const stepFields = ['title', 'description', 'action', 'content', 'selector', 'voiceContent'];
      
      stepFields.forEach(field => {
        if (fromStep[field] !== toStep[field]) {
          stepFieldChanges.push({
            field,
            oldValue: fromStep[field],
            newValue: toStep[field],
            type: 'modified',
          });
        }
      });

      if (stepFieldChanges.length > 0) {
        stepChanges.push({
          stepIndex: i,
          stepId: toStep.id || fromStep.id,
          changes: stepFieldChanges,
          type: 'modified',
        });
      } else {
        stepChanges.push({
          stepIndex: i,
          stepId: toStep.id || fromStep.id,
          changes: [],
          type: 'unchanged',
        });
      }
    }
  }

  return {
    changes,
    stepChanges,
    summary: {
      totalChanges: changes.length + stepChanges.filter(sc => sc.type !== 'unchanged').length,
      basicFieldChanges: changes.length,
      stepChanges: stepChanges.filter(sc => sc.type !== 'unchanged').length,
      stepsAdded: stepChanges.filter(sc => sc.type === 'added').length,
      stepsRemoved: stepChanges.filter(sc => sc.type === 'removed').length,
      stepsModified: stepChanges.filter(sc => sc.type === 'modified').length,
    },
  };
}
