import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { prisma } from "@/lib/database";
import { AdaptiveLearningService } from "@/lib/adaptive-learning";

// GET /api/users/[userId]/recommendations - Get learning recommendations
export async function GET(
  request: NextRequest,
  { params }: { params: { userId: string } }
) {
  try {
    const session = await getServerSession();

    if (!session?.user?.email) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const user = await prisma.user.findUnique({
      where: { email: session.user.email },
    });

    if (!user) {
      return NextResponse.json({ error: "User not found" }, { status: 404 });
    }

    const targetUserId = params.userId;

    // Check if user can access recommendations (self only)
    if (user.id !== targetUserId) {
      return NextResponse.json({ error: "Forbidden" }, { status: 403 });
    }

    const { searchParams } = new URL(request.url);
    const includeExpired = searchParams.get("includeExpired") === "true";
    const type = searchParams.get("type");

    // Get existing recommendations from database
    const whereClause: any = {
      userId: targetUserId,
    };

    if (!includeExpired) {
      whereClause.OR = [
        { expiresAt: null },
        { expiresAt: { gt: new Date() } },
      ];
    }

    if (type) {
      whereClause.type = type;
    }

    const existingRecommendations = await prisma.learningRecommendation.findMany({
      where: whereClause,
      include: {
        tutorial: {
          select: {
            id: true,
            title: true,
            description: true,
            metadata: true,
          },
        },
      },
      orderBy: { createdAt: "desc" },
    });

    // Generate new recommendations
    const newRecommendations = await AdaptiveLearningService.generateRecommendations(targetUserId);

    // Save new recommendations to database
    const savedRecommendations = await Promise.all(
      newRecommendations.map(async (rec) => {
        // Check if similar recommendation already exists
        const existing = await prisma.learningRecommendation.findFirst({
          where: {
            userId: targetUserId,
            type: rec.type,
            tutorialId: rec.tutorialId,
            createdAt: { gt: new Date(Date.now() - 24 * 60 * 60 * 1000) }, // Within last 24 hours
          },
        });

        if (existing) {
          return existing;
        }

        // Create new recommendation
        return await prisma.learningRecommendation.create({
          data: {
            userId: targetUserId,
            tutorialId: rec.tutorialId,
            type: rec.type,
            title: rec.title,
            description: rec.description,
            confidence: rec.confidence,
            reasoning: rec.reasoning,
            metadata: rec.metadata,
            expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // Expire in 7 days
          },
          include: {
            tutorial: {
              select: {
                id: true,
                title: true,
                description: true,
                metadata: true,
              },
            },
          },
        });
      })
    );

    // Combine existing and new recommendations
    const allRecommendations = [
      ...existingRecommendations,
      ...savedRecommendations.filter(rec => 
        !existingRecommendations.some(existing => existing.id === rec.id)
      ),
    ];

    // Sort by confidence and creation date
    allRecommendations.sort((a, b) => {
      if (a.confidence !== b.confidence) {
        return b.confidence - a.confidence;
      }
      return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
    });

    // Log analytics
    await prisma.analytics.create({
      data: {
        userId: targetUserId,
        action: "recommendations_viewed",
        metadata: {
          recommendationCount: allRecommendations.length,
          newRecommendations: savedRecommendations.length,
          types: [...new Set(allRecommendations.map(r => r.type))],
        },
      },
    });

    return NextResponse.json({
      recommendations: allRecommendations,
      total: allRecommendations.length,
      new: savedRecommendations.length,
    });
  } catch (error) {
    console.error("Error fetching recommendations:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

// POST /api/users/[userId]/recommendations/[recommendationId]/accept - Accept a recommendation
export async function POST(
  request: NextRequest,
  { params }: { params: { userId: string } }
) {
  try {
    const session = await getServerSession();

    if (!session?.user?.email) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const user = await prisma.user.findUnique({
      where: { email: session.user.email },
    });

    if (!user) {
      return NextResponse.json({ error: "User not found" }, { status: 404 });
    }

    const targetUserId = params.userId;

    // Check if user can accept recommendations (self only)
    if (user.id !== targetUserId) {
      return NextResponse.json({ error: "Forbidden" }, { status: 403 });
    }

    const body = await request.json();
    const { recommendationId, accepted } = body;

    if (!recommendationId || typeof accepted !== "boolean") {
      return NextResponse.json(
        { error: "Recommendation ID and accepted status are required" },
        { status: 400 }
      );
    }

    // Update recommendation
    const updatedRecommendation = await prisma.learningRecommendation.update({
      where: { id: recommendationId },
      data: {
        isAccepted: accepted,
        acceptedAt: accepted ? new Date() : null,
      },
    });

    // Log analytics
    await prisma.analytics.create({
      data: {
        userId: targetUserId,
        action: accepted ? "recommendation_accepted" : "recommendation_rejected",
        metadata: {
          recommendationId,
          recommendationType: updatedRecommendation.type,
          confidence: updatedRecommendation.confidence,
        },
      },
    });

    // If accepted and it's a tutorial suggestion, create a tutorial progress entry
    if (accepted && updatedRecommendation.type === "tutorial_suggestion" && updatedRecommendation.tutorialId) {
      await prisma.tutorialProgress.upsert({
        where: {
          tutorialId_userId: {
            tutorialId: updatedRecommendation.tutorialId,
            userId: targetUserId,
          },
        },
        update: {},
        create: {
          tutorialId: updatedRecommendation.tutorialId,
          userId: targetUserId,
          currentStep: 0,
          progress: 0,
        },
      });
    }

    return NextResponse.json({
      message: `Recommendation ${accepted ? "accepted" : "rejected"} successfully`,
      recommendation: updatedRecommendation,
    });
  } catch (error) {
    console.error("Error updating recommendation:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
