import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { prisma } from "@/lib/database";
import { hasPermission } from "@/lib/permissions";

// GET /api/tutorials/[id]/analytics - Get comprehensive tutorial analytics
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession();

    if (!session?.user?.email) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const user = await prisma.user.findUnique({
      where: { email: session.user.email },
    });

    if (!user) {
      return NextResponse.json({ error: "User not found" }, { status: 404 });
    }

    const tutorialId = params.id;
    const { searchParams } = new URL(request.url);
    const timeRange = searchParams.get("timeRange") || "30d"; // 7d, 30d, 90d, 1y
    const includeStepAnalytics = searchParams.get("includeSteps") === "true";

    // Check if tutorial exists and user has permission
    const tutorial = await prisma.tutorial.findUnique({
      where: { id: tutorialId },
      include: {
        collaborators: true,
      },
    });

    if (!tutorial) {
      return NextResponse.json({ error: "Tutorial not found" }, { status: 404 });
    }

    // Check permissions
    const canRead = 
      tutorial.createdBy === user.id ||
      tutorial.collaborators.some(c => c.userId === user.id) ||
      await hasPermission(user, "ANALYTICS_READ");

    if (!canRead) {
      return NextResponse.json({ error: "Forbidden" }, { status: 403 });
    }

    // Calculate date range
    const endDate = new Date();
    const startDate = new Date();
    switch (timeRange) {
      case "7d":
        startDate.setDate(endDate.getDate() - 7);
        break;
      case "30d":
        startDate.setDate(endDate.getDate() - 30);
        break;
      case "90d":
        startDate.setDate(endDate.getDate() - 90);
        break;
      case "1y":
        startDate.setFullYear(endDate.getFullYear() - 1);
        break;
      default:
        startDate.setDate(endDate.getDate() - 30);
    }

    // Get basic analytics
    const [
      totalViews,
      uniqueUsers,
      completions,
      averageCompletionTime,
      dropOffAnalytics,
      ratingAnalytics,
      timeSeriesData,
    ] = await Promise.all([
      // Total views
      prisma.analytics.count({
        where: {
          tutorialId,
          action: "tutorial_viewed",
          timestamp: { gte: startDate, lte: endDate },
        },
      }),

      // Unique users
      prisma.analytics.groupBy({
        by: ["userId"],
        where: {
          tutorialId,
          action: "tutorial_viewed",
          timestamp: { gte: startDate, lte: endDate },
        },
        _count: { userId: true },
      }),

      // Completions
      prisma.tutorialProgress.count({
        where: {
          tutorialId,
          completed: true,
          completedAt: { gte: startDate, lte: endDate },
        },
      }),

      // Average completion time
      prisma.tutorialProgress.aggregate({
        where: {
          tutorialId,
          completed: true,
          completedAt: { gte: startDate, lte: endDate },
        },
        _avg: { timeSpent: true },
      }),

      // Drop-off analytics
      prisma.tutorialProgress.groupBy({
        by: ["currentStep"],
        where: {
          tutorialId,
          completed: false,
          startedAt: { gte: startDate, lte: endDate },
        },
        _count: { currentStep: true },
      }),

      // Rating analytics from feedback
      prisma.$queryRaw`
        SELECT 
          AVG(CAST(metadata->>'rating' AS FLOAT)) as average_rating,
          COUNT(*) as total_ratings
        FROM "Analytics"
        WHERE "tutorialId" = ${tutorialId}
          AND "action" = 'tutorial_feedback'
          AND "timestamp" >= ${startDate}
          AND "timestamp" <= ${endDate}
          AND metadata->>'rating' IS NOT NULL
      `,

      // Time series data for views and completions
      prisma.$queryRaw`
        SELECT 
          DATE_TRUNC('day', timestamp) as date,
          action,
          COUNT(*) as count
        FROM "Analytics"
        WHERE "tutorialId" = ${tutorialId}
          AND "action" IN ('tutorial_viewed', 'tutorial_completed')
          AND "timestamp" >= ${startDate}
          AND "timestamp" <= ${endDate}
        GROUP BY DATE_TRUNC('day', timestamp), action
        ORDER BY date
      `,
    ]);

    // Calculate completion rate
    const totalStarts = await prisma.tutorialProgress.count({
      where: {
        tutorialId,
        startedAt: { gte: startDate, lte: endDate },
      },
    });

    const completionRate = totalStarts > 0 ? (completions / totalStarts) * 100 : 0;

    // Get step-by-step analytics if requested
    let stepAnalytics = null;
    if (includeStepAnalytics) {
      const steps = typeof tutorial.steps === 'string' 
        ? JSON.parse(tutorial.steps) 
        : tutorial.steps;

      stepAnalytics = await Promise.all(
        steps.map(async (step: any, index: number) => {
          const [stepViews, stepCompletions, stepErrors] = await Promise.all([
            // Step views
            prisma.analytics.count({
              where: {
                tutorialId,
                action: "tutorial_step_viewed",
                metadata: {
                  path: ["stepIndex"],
                  equals: index,
                },
                timestamp: { gte: startDate, lte: endDate },
              },
            }),

            // Step completions
            prisma.analytics.count({
              where: {
                tutorialId,
                action: "tutorial_step_completed",
                metadata: {
                  path: ["stepIndex"],
                  equals: index,
                },
                timestamp: { gte: startDate, lte: endDate },
              },
            }),

            // Step errors
            prisma.analytics.count({
              where: {
                tutorialId,
                action: "tutorial_step_error",
                metadata: {
                  path: ["stepIndex"],
                  equals: index,
                },
                timestamp: { gte: startDate, lte: endDate },
              },
            }),
          ]);

          return {
            stepIndex: index,
            stepId: step.id,
            stepTitle: step.title,
            views: stepViews,
            completions: stepCompletions,
            errors: stepErrors,
            completionRate: stepViews > 0 ? (stepCompletions / stepViews) * 100 : 0,
            errorRate: stepViews > 0 ? (stepErrors / stepViews) * 100 : 0,
          };
        })
      );
    }

    // Get user engagement metrics
    const engagementMetrics = await prisma.$queryRaw`
      SELECT 
        AVG(CAST(metadata->>'timeSpent' AS INTEGER)) as avg_time_per_session,
        AVG(CAST(metadata->>'stepsCompleted' AS INTEGER)) as avg_steps_per_session,
        COUNT(DISTINCT "userId") as active_users
      FROM "Analytics"
      WHERE "tutorialId" = ${tutorialId}
        AND "action" = 'tutorial_session_end'
        AND "timestamp" >= ${startDate}
        AND "timestamp" <= ${endDate}
    `;

    // Get device and browser analytics
    const deviceAnalytics = await prisma.$queryRaw`
      SELECT 
        metadata->>'device' as device_type,
        metadata->>'browser' as browser,
        COUNT(*) as count
      FROM "Analytics"
      WHERE "tutorialId" = ${tutorialId}
        AND "action" = 'tutorial_viewed'
        AND "timestamp" >= ${startDate}
        AND "timestamp" <= ${endDate}
        AND metadata->>'device' IS NOT NULL
      GROUP BY metadata->>'device', metadata->>'browser'
      ORDER BY count DESC
    `;

    // Get geographic analytics
    const geoAnalytics = await prisma.$queryRaw`
      SELECT 
        metadata->>'country' as country,
        metadata->>'region' as region,
        COUNT(*) as count
      FROM "Analytics"
      WHERE "tutorialId" = ${tutorialId}
        AND "action" = 'tutorial_viewed'
        AND "timestamp" >= ${startDate}
        AND "timestamp" <= ${endDate}
        AND metadata->>'country' IS NOT NULL
      GROUP BY metadata->>'country', metadata->>'region'
      ORDER BY count DESC
      LIMIT 20
    `;

    const analytics = {
      overview: {
        totalViews,
        uniqueUsers: uniqueUsers.length,
        completions,
        completionRate: Math.round(completionRate * 100) / 100,
        averageCompletionTime: Math.round((averageCompletionTime._avg.timeSpent || 0) / 60), // Convert to minutes
        averageRating: ratingAnalytics[0]?.average_rating 
          ? Math.round(parseFloat(ratingAnalytics[0].average_rating) * 10) / 10 
          : 0,
        totalRatings: parseInt(ratingAnalytics[0]?.total_ratings || "0"),
      },
      dropOffAnalytics: dropOffAnalytics.map(item => ({
        step: item.currentStep,
        dropOffs: item._count.currentStep,
      })),
      timeSeriesData,
      stepAnalytics,
      engagement: engagementMetrics[0] || {
        avg_time_per_session: 0,
        avg_steps_per_session: 0,
        active_users: 0,
      },
      deviceAnalytics,
      geoAnalytics,
      timeRange,
      dateRange: {
        start: startDate.toISOString(),
        end: endDate.toISOString(),
      },
    };

    return NextResponse.json(analytics);
  } catch (error) {
    console.error("Error fetching tutorial analytics:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
