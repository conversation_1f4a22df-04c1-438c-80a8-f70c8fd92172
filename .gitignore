# dependencies
/node_modules
/.pnp
.pnp.*
.yarn/*
!.yarn/patches
!.yarn/plugins
!.yarn/releases
!.yarn/versions

# history
history/

# testing
/coverage

# next.js
/.next/
/out/

# production
/build

# misc
.DS_Store
*.pem

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# local env files
.env*.local
.env

# vercel
.vercel

# typescript
*.tsbuildinfo
next-env.d.ts

# tempo-devtools
tempo-devtools/

# remove git history
.git
.node_modules
.history
.next
.out
.build
.DS_Store
.env
.env.local
.env.development.local
.env.test.local
.env.production.local
